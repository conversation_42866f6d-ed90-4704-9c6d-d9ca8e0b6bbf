package com.tem.customer.model.dto.partner;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 微信用户绑定关系请求DTO
 * 用于接收前端请求参数
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@Accessors(chain = true)
public class WechatUserBindingDTO {

    /**
     * 记录ID（更新时必填）
     */
    private Long id;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long partnerId;

    /**
     * 微信UnionID
     */
    @Size(max = 64, message = "微信UnionID长度不能超过64个字符")
    private String unionId;

    /**
     * 来源类型：WORK_WECHAT-企业微信，OFFICIAL_ACCOUNT-公众号，MINI_PROGRAM-小程序，WECHAT_H5-微信H5(包括企微用户私聊）
     */
    @NotBlank(message = "来源类型不能为空")
    @Size(max = 20, message = "来源类型长度不能超过20个字符")
    private String sourceType;

    /**
     * 来源应用ID（公众号appid、小程序appid等）
     */
    @Size(max = 64, message = "来源应用ID长度不能超过64个字符")
    private String sourceAppId;

    /**
     * 绑定的用户ID（SaaS系统中的用户）
     */
    private Long userId;

    /**
     * 企业微信群ID，用于群内人员绑定场景
     */
    @Size(max = 64, message = "微信群ID长度不能超过64个字符")
    private String chatId;

    /**
     * 状态：1-有效，0-无效
     */
    @Min(value = 0, message = "状态值必须为0或1")
    @Max(value = 1, message = "状态值必须为0或1")
    private Integer status;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
