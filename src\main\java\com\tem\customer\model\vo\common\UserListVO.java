package com.tem.customer.model.vo.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 企业用户列表响应VO
 * 用于返回给前端的用户基本信息
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@Accessors(chain = true)
public class UserListVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * VIP级别：0-否，1-是，2-BOSS
     */
    private Integer vipLevel;

    /**
     * VIP标识（处理后的值，用于前端显示）
     */
    private Integer vip;

    /**
     * 企业ID
     */
    private Long partnerId;

    /**
     * 企业名称
     */
    private String partnerName;

    /**
     * 部门路径
     */
    private String deptPath;

    /**
     * 用户类型：0-可登录用户，1-不可登录用户
     */
    private Integer type;

    /**
     * 账号授权状态
     * 0：未审核，1：已审核，-99：已失效，员工已离开公司，-10：已冻结
     */
    private Integer status;

    /**
     * 企业备注数量
     */
    private Integer partnerCount;

    /**
     * 用户备注数量
     */
    private Integer userCount;

    /**
     * 性别：0-男，1-女
     */
    private Integer gender;

    /**
     * 性别描述
     */
    private String genderDesc;

    /**
     * 部门全路径名称
     */
    private String orgPathNames;

    /**
     * 职级名称
     */
    private String empLevelName;

    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        if (gender == null) {
            return "未知";
        }
        return switch (gender) {
            case 0 -> "男";
            case 1 -> "女";
            default -> "未知";
        };
    }

    /**
     * 获取VIP描述
     */
    public String getVipDesc() {
        if (vipLevel == null) {
            return "否";
        }
        return switch (vipLevel) {
            case 1 -> "VIP";
            case 2 -> "BOSS";
            default -> "否";
        };
    }

    /**
     * 获取用户状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        return switch (status) {
            case 0 -> "未审核";
            case 1 -> "已审核";
            case -10 -> "已冻结";
            case -99 -> "已失效";
            default -> "未知";
        };
    }

    /**
     * 获取用户类型描述
     */
    public String getTypeDesc() {
        if (type == null) {
            return "未知";
        }
        return switch (type) {
            case 0 -> "可登录用户";
            case 1 -> "不可登录用户";
            default -> "未知";
        };
    }

    /**
     * 设置VIP标识（根据vipLevel设置vip字段）
     */
    public UserListVO setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
        this.vip = vipLevel; // 保持兼容性
        return this;
    }
}
