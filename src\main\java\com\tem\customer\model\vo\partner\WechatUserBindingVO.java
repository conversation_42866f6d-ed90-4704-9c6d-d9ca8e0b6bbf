package com.tem.customer.model.vo.partner;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 微信用户绑定关系响应VO
 * 用于返回给前端的数据
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@Accessors(chain = true)
public class WechatUserBindingVO {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long partnerId;

    /**
     * 企业名称
     */
    private String partnerName;

    /**
     * 微信UnionID
     */
    private String unionId;

    /**
     * 来源类型：WORK_WECHAT-企业微信，OFFICIAL_ACCOUNT-公众号，MINI_PROGRAM-小程序，WECHAT_H5-微信H5，GROUP_STAFF-群内人员
     */
    private String sourceType;

    /**
     * 来源类型描述
     */
    private String sourceTypeDesc;

    /**
     * 来源应用ID（公众号appid、小程序appid等）
     */
    private String sourceAppId;

    /**
     * 绑定的用户ID（SaaS系统中的用户）
     */
    private Long userId;

    /**
     * 绑定的用户名称
     */
    private String userName;
    private String mobile;
    private String email;

    /**
     * 企业微信群ID，用于群内人员绑定场景
     */
    private String chatId;

    /**
     * 微信群名称（关联查询）
     */
    private String groupName;

    /**
     * 状态：1-有效，0-无效
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
