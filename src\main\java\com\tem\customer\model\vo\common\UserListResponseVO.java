package com.tem.customer.model.vo.common;

import com.tem.pss.dto.serverSkill.ServerSkillResponseDto;
import com.tem.pss.dto.serverSkill.SkillDto;
import com.tem.pss.dto.task.ReceiveTaskType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户列表完整响应VO
 * 包含用户列表、权限信息和服务技能信息
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@Accessors(chain = true)
public class UserListResponseVO {

    /**
     * 用户列表
     */
    private List<UserListVO> userList;

    /**
     * 代客下单权限：0-有权限，其他-无权限
     */
    private Integer customerProxyKill;

    /**
     * 手工录单权限：0-有权限，其他-无权限
     */
    private Integer manualKill;

    /**
     * 服务技能信息
     */
    private ServerSkillVO serverSkill;

    /**
     * 配置信息
     */
    private String www;

    /**
     * 服务技能VO
     */
    @Data
    @Accessors(chain = true)
    public static class ServerSkillVO {

        private Long userId;

        private String userName;

        private Long tmcId;

        private Map<String, Integer> skills;

        public Map<String, Integer> getAllSkill(Boolean isHasSkill) {
            Integer val = isHasSkill ? 1 : 0;
            ReceiveTaskType[] receiveTaskTypes = ReceiveTaskType.values();
            Map<String, Integer> skillMap = new HashMap<>(receiveTaskTypes.length);
            for (ReceiveTaskType receiveTaskType : receiveTaskTypes) {
                skillMap.put(receiveTaskType.name(), val);
            }
            return skillMap;
        }


        public ServerSkillVO(ServerSkillResponseDto serverSkillResponseDto, Long userId, Long tmcId, String userName) {
            Map<String, Integer> skillMap = getAllSkill(false);
            if (serverSkillResponseDto != null) {
                List<SkillDto> skills = serverSkillResponseDto.getSkills();
                skills.forEach(e -> skillMap.put(e.getSkillType(), 1));
                this.userId = serverSkillResponseDto.getUserId();
                this.tmcId = serverSkillResponseDto.getTmcId();
                this.userName = serverSkillResponseDto.getFullname();
            } else {
                this.userId = userId;
                this.tmcId = tmcId;
                this.userName = userName;
            }
            this.skills = skillMap;
        }

        public void hasAllSkills() {
            this.skills = getAllSkill(true);
        }
    }
}
