<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tem.customer.repository.mapper.PartnerWechatGroupMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tem.customer.repository.entity.PartnerWechatGroup">
        <id column="id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="chat_id" property="chatId"/>
        <result column="group_type" property="groupType"/>
        <result column="status" property="status"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        partner_id,
        chat_id,
        group_type,
        status,
        sort_order,
        remark,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version
    </sql>

    <!-- 根据企业ID查询微信群列表，按排序字段升序排列 -->
    <select id="selectByPartnerIdOrderBySort" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_partner_wechat_group
        WHERE partner_id = #{partnerId}
          AND deleted = 0
        ORDER BY sort_order, create_time
    </select>

    <!-- 根据企业ID查询启用状态的微信群列表，按排序字段升序排列 -->
    <select id="selectEnabledByPartnerIdOrderBySort" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_partner_wechat_group
        WHERE partner_id = #{partnerId}
          AND status = 1
          AND deleted = 0
        ORDER BY sort_order, create_time
    </select>

    <!-- 根据企业ID统计微信群数量 -->
    <select id="countByPartnerId" resultType="int">
        SELECT COUNT(1)
        FROM t_partner_wechat_group
        WHERE partner_id = #{partnerId}
          AND deleted = 0
    </select>

    <!-- 根据企业ID统计启用状态的微信群数量 -->
    <select id="countEnabledByPartnerId" resultType="int">
        SELECT COUNT(1)
        FROM t_partner_wechat_group
        WHERE partner_id = #{partnerId}
          AND status = 1
          AND deleted = 0
    </select>

    <!-- 根据企业ID获取微信群的最大排序值 -->
    <select id="getMaxSortOrderByPartnerId" resultType="java.lang.Integer">
        SELECT MAX(sort_order)
        FROM t_partner_wechat_group
        WHERE partner_id = #{partnerId}
          AND deleted = 0
    </select>

    <!-- 根据chatId查询微信群信息 -->
    <select id="selectByChatId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_partner_wechat_group
        WHERE chat_id = #{chatId}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 检查chatId是否已存在（排除指定ID） -->
    <select id="existsByChatIdExcludeId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM t_partner_wechat_group
        WHERE chat_id = #{chatId}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 物理删除企业微信群绑定关系 -->
    <delete id="deleteByIdPhysically">
        DELETE FROM t_partner_wechat_group
        WHERE id = #{id}
    </delete>
</mapper>
