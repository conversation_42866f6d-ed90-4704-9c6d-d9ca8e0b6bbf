package com.tem.customer;

import cn.dev33.satoken.SaManager;
import com.iplatform.common.listener.ApolloLoggingApplicationListener;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.infrastructure.config.ApolloApplicationListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;

import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.tem.customer", "com.iplatform"}
       /* exclude = {
            org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
            org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration.class
        }*/)
@EnableDubbo
@MapperScan(value = "com.tem.customer.repository.mapper")
public class CustomerAdminWebApplication implements ApplicationRunner {

    private final Environment environment;

    public CustomerAdminWebApplication(Environment environment) {
        this.environment = environment;
    }

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(CustomerAdminWebApplication.class);
        Set<ApplicationListener<?>> listeners = app.getListeners();
        ApolloApplicationListener apolloApplicationListener = new ApolloApplicationListener();
        Set<ApplicationListener<?>> filterListeners = listeners.stream().filter(v -> v.getClass() != ApolloLoggingApplicationListener.class).collect(Collectors.toSet());
        filterListeners.add(apolloApplicationListener);
        app.setListeners(filterListeners);
        app.run(args);
    }

    @Override
    public void run(ApplicationArguments args) {
        System.out.printf("启动端口：%s %n", environment.getProperty("server.port"));
        LogUtils.info(log, "启动成功，Sa-Token 配置如下：{}", SaManager.getConfig());
    }


}
