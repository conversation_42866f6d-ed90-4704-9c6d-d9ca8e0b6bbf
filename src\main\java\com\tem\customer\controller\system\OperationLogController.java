package com.tem.customer.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tem.customer.shared.common.Result;
import com.tem.customer.model.dto.system.OperationLogQueryDTO;
import com.tem.customer.repository.entity.OperationLog;
import com.tem.customer.model.vo.system.OperationLogVO;
import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;
import com.tem.customer.service.system.OperationLogService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 操作记录日志控制器
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/operation-logs")
@RequiredArgsConstructor
@Validated
public class OperationLogController {

    private final OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    public Result<IPage<OperationLogVO>> pageQuery(@RequestBody @Valid OperationLogQueryDTO queryDTO) {
        IPage<OperationLog> page = operationLogService.pageQuery(queryDTO);

        // 转换为VO
        IPage<OperationLogVO> voPage = page.convert(this::convertToVO);

        return Result.success(voPage);
    }

    /**
     * 根据业务类型和业务ID查询操作日志
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 操作日志列表
     */
    @GetMapping("/business/{businessType}/{businessId}")
    public Result<List<OperationLogVO>> listByBusinessTypeAndId(
            @PathVariable @NotNull String businessType,
            @PathVariable @NotNull Long businessId) {

        BusinessType type = BusinessType.fromCode(businessType);
        List<OperationLog> logs = operationLogService.listByBusinessTypeAndId(type, businessId);

        List<OperationLogVO> voList = logs.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return Result.success(voList);
    }

    /**
     * 根据目标企业ID查询操作日志
     *
     * @param targetPartnerId 目标企业ID
     * @return 操作日志列表
     */
    @GetMapping("/partner/{targetPartnerId}")
    public Result<List<OperationLogVO>> listByTargetPartnerId(@PathVariable @NotNull Long targetPartnerId) {
        List<OperationLog> logs = operationLogService.listByTargetPartnerId(targetPartnerId);

        List<OperationLogVO> voList = logs.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return Result.success(voList);
    }

    /**
     * 根据操作人ID查询操作日志
     *
     * @param operatorId 操作人ID
     * @return 操作日志列表
     */
    @GetMapping("/operator/{operatorId}")
    public Result<List<OperationLogVO>> listByOperatorId(@PathVariable @NotNull Long operatorId) {
        List<OperationLog> logs = operationLogService.listByOperatorId(operatorId);

        List<OperationLogVO> voList = logs.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return Result.success(voList);
    }

    /**
     * 根据ID查询操作日志详情
     *
     * @param id 操作日志ID
     * @return 操作日志详情
     */
    @GetMapping("/{id}")
    public Result<OperationLogVO> getById(@PathVariable @NotNull Long id) {
        OperationLog log = operationLogService.getById(id);
        if (log == null) {
            return Result.error("操作日志不存在");
        }

        OperationLogVO vo = convertToVO(log);
        return Result.success(vo);
    }

    /**
     * 获取业务类型列表
     *
     * @return 业务类型列表
     */
    @GetMapping("/business-types")
    public Result<List<BusinessTypeVO>> getBusinessTypes() {
        List<BusinessTypeVO> types = Stream.of(BusinessType.values())
                .map(type -> new BusinessTypeVO(type.getCode(), type.getDescription()))
                .collect(Collectors.toList());

        return Result.success(types);
    }

    /**
     * 获取操作类型列表
     *
     * @return 操作类型列表
     */
    @GetMapping("/operation-types")
    public Result<List<OperationTypeVO>> getOperationTypes() {
        List<OperationTypeVO> types = Stream.of(OperationType.values())
                .map(type -> new OperationTypeVO(type.getCode(), type.getDescription()))
                .collect(Collectors.toList());

        return Result.success(types);
    }

    /**
     * 转换为VO对象
     *
     * @param log 操作日志实体
     * @return VO对象
     */
    private OperationLogVO convertToVO(OperationLog log) {
        if (log == null) {
            return null;
        }

        OperationLogVO vo = new OperationLogVO();
        vo.setId(log.getId())
                .setBusinessType(log.getBusinessType())
                .setBusinessTypeDesc(BusinessType.fromCode(log.getBusinessType()).getDescription())
                .setBusinessId(log.getBusinessId())
                .setOperationType(log.getOperationType())
                .setOperationTypeDesc(OperationType.fromCode(log.getOperationType()).getDescription())
                .setOperationDesc(log.getOperationDesc())
                .setOperatorId(log.getOperatorId())
                .setOperatorName(log.getOperatorName())
                .setOperatorUsername(log.getOperatorUsername())
                .setPartnerId(log.getPartnerId())
                .setTargetPartnerId(log.getTargetPartnerId())
                .setIpAddress(log.getIpAddress())
                .setUserAgent(log.getUserAgent())
                .setRequestUri(log.getRequestUri())
                .setRequestMethod(log.getRequestMethod())
                .setExecutionTime(log.getExecutionTime())
                .setCreateTime(log.getCreateTime())
                .setCreateBy(log.getCreateBy());

        return vo;
    }

    /**
     * 业务类型VO
     */
    @Setter
    @Getter
    public static class BusinessTypeVO {
        // getters and setters
        private String code;
        private String description;

        public BusinessTypeVO(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }

    /**
     * 操作类型VO
     */
    @Setter
    @Getter
    public static class OperationTypeVO {
        // getters and setters
        private String code;
        private String description;

        public OperationTypeVO(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }
}
