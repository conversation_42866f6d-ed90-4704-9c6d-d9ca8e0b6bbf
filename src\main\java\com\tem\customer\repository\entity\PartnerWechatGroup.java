package com.tem.customer.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 企业微信群绑定关系实体类
 * 用于管理企业与企业微信群的绑定关系，支持一个企业绑定多个微信群
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_partner_wechat_group")
public class PartnerWechatGroup extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业ID，关联企业表
     */
    @TableField("partner_id")
    private Long partnerId;

    /**
     * 企业微信群ID，企业微信群的唯一标识
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 群类型：CUSTOMER_SERVICE-客服群
     */
    @TableField("group_type")
    private String groupType;

    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 排序字段，数值越小越靠前
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 群类型枚举
     */
    public enum GroupType {
        /**
         * 客服群
         */
        CUSTOMER_SERVICE("CUSTOMER_SERVICE", "客服群");

        private final String code;
        private final String desc;

        GroupType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        /**
         * 禁用
         */
        DISABLED(0, "禁用"),
        /**
         * 启用
         */
        ENABLED(1, "启用");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
