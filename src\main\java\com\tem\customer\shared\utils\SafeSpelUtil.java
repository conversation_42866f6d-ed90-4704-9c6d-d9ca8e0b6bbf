package com.tem.customer.shared.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Set;

/**
 * 安全的SpEL表达式工具类
 * 提供受限制的SpEL表达式解析，防止安全漏洞
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
public class SafeSpelUtil {

    private static final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 危险的方法名黑名单
     */
    private static final Set<String> DANGEROUS_METHODS = Set.of(
            "getClass", "forName", "newInstance", "getClassLoader",
            "getMethod", "getDeclaredMethod", "invoke", "exec",
            "getRuntime", "exit", "halt", "load", "loadLibrary"
    );

    /**
     * 危险的类名黑名单
     */
    private static final Set<String> DANGEROUS_CLASSES = Set.of(
            "java.lang.Runtime", "java.lang.ProcessBuilder", "java.lang.System",
            "java.lang.Class", "java.lang.ClassLoader", "java.io.File",
            "java.nio.file.Files", "java.nio.file.Paths"
    );

    /**
     * 安全解析SpEL表达式
     *
     * @param expression SpEL表达式
     * @param method     方法对象
     * @param args       方法参数
     * @param result     方法返回结果
     * @return 解析结果
     */
    public static Object safeParseExpression(String expression, Method method, Object[] args, Object result) {
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }

        // 安全检查
        if (!isSafeExpression(expression)) {
            log.warn("检测到不安全的SpEL表达式: {}", expression);
            return null;
        }

        try {
            EvaluationContext context = createSafeEvaluationContext(method, args, result);
            Expression expr = parser.parseExpression(expression);
            return expr.getValue(context);
        } catch (Exception e) {
            // 增强错误信息，帮助定位问题
            String errorDetail = String.format("表达式: %s, result类型: %s, result值: %s",
                expression,
                result != null ? result.getClass().getSimpleName() : "null",
                result);
            log.warn("SpEL表达式解析失败: {}", errorDetail, e);
            return null;
        }
    }

    /**
     * 检查表达式是否安全
     *
     * @param expression SpEL表达式
     * @return 如果安全返回true，否则返回false
     */
    private static boolean isSafeExpression(String expression) {
        if (expression == null) {
            return false;
        }

        String lowerExpression = expression.toLowerCase().trim();

        // 检查危险方法
        for (String dangerousMethod : DANGEROUS_METHODS) {
            if (containsMethod(lowerExpression, dangerousMethod.toLowerCase())) {
                return false;
            }
        }

        // 检查危险类
        for (String dangerousClass : DANGEROUS_CLASSES) {
            if (lowerExpression.contains(dangerousClass.toLowerCase())) {
                return false;
            }
        }

        // 检查危险字符和模式
        if (lowerExpression.contains("t(") || // T()操作符，用于访问类型
                lowerExpression.contains("@") ||  // Bean引用
                lowerExpression.contains("new ") || // 构造函数调用
                lowerExpression.contains("class") || // 类访问
                lowerExpression.contains("getclass") || // getClass方法
                lowerExpression.contains("reflect") || // 反射相关
                lowerExpression.contains("invoke") || // 方法调用
                lowerExpression.contains("constructor") || // 构造器
                lowerExpression.contains("field") || // 字段访问
                lowerExpression.contains("method")) { // 方法访问
            return false;
        }

        return true;
    }

    /**
     * 检查表达式中是否包含指定的方法调用
     *
     * @param expression 表达式
     * @param methodName 方法名
     * @return 如果包含返回true，否则返回false
     */
    private static boolean containsMethod(String expression, String methodName) {
        // 检查方法调用模式：methodName( 或 .methodName(
        return expression.contains(methodName + "(") ||
               expression.contains("." + methodName + "(");
    }

    /**
     * 创建安全的评估上下文
     *
     * @param method 方法对象
     * @param args   方法参数
     * @param result 方法返回结果
     * @return 评估上下文
     */
    private static EvaluationContext createSafeEvaluationContext(Method method, Object[] args, Object result) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 禁用类型解析器，防止T()操作符
        context.setTypeLocator(typeName -> {
            throw new UnsupportedOperationException("Type access is not allowed for security reasons");
        });

        // 清空方法解析器，防止调用危险方法
        context.getMethodResolvers().clear();

        // 设置方法参数
        if (method != null && args != null) {
            Parameter[] parameters = method.getParameters();
            for (int i = 0; i < parameters.length && i < args.length; i++) {
                String paramName = parameters[i].getName();
                // 只允许访问基本类型和安全的对象
                if (isSafeObject(args[i])) {
                    context.setVariable(paramName, args[i]);
                }
            }
        }

        // 设置返回结果，即使result为null也要设置，这样SpEL表达式可以正确处理null值
        if (isSafeObject(result)) {
            context.setVariable("result", result);
        } else if (result == null) {
            // 显式设置null值，避免变量未定义错误
            context.setVariable("result", null);
        }

        return context;
    }

    /**
     * 检查对象是否安全
     *
     * @param obj 对象
     * @return 如果安全返回true，否则返回false
     */
    private static boolean isSafeObject(Object obj) {
        if (obj == null) {
            return true;
        }

        Class<?> clazz = obj.getClass();
        String className = clazz.getName();

        // 允许基本类型和包装类型
        if (clazz.isPrimitive()) {
            return true;
        }

        // 允许常用的安全包装类型
        if (clazz == String.class ||
            clazz == Integer.class ||
            clazz == Long.class ||
            clazz == Double.class ||
            clazz == Float.class ||
            clazz == Boolean.class ||
            clazz == Byte.class ||
            clazz == Short.class ||
            clazz == Character.class) {
            return true;
        }

        // 允许业务对象（在com.tem包下）
        if (className.startsWith("com.tem.")) {
            return true;
        }

        // 其他类型不允许
        return false;
    }

    /**
     * 安全解析Long类型的表达式结果
     *
     * @param expression SpEL表达式
     * @param method     方法对象
     * @param args       方法参数
     * @param result     方法返回结果
     * @return Long值，解析失败返回null
     */
    public static Long parseLongExpression(String expression, Method method, Object[] args, Object result) {
        // 表达式为空直接返回null
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }

        // 如果表达式包含result但result为null，直接返回null避免NPE
        if (expression.contains("#result") && result == null) {
            log.warn("SpEL表达式 {} 引用了#result，但result为null", expression);
            return null;
        }

        Object value = safeParseExpression(expression, method, args, result);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Long: {}", value);
            }
        }
        return null;
    }

    /**
     * 安全解析String类型的表达式结果
     *
     * @param expression SpEL表达式
     * @param method     方法对象
     * @param args       方法参数
     * @param result     方法返回结果
     * @return String值，解析失败返回null
     */
    public static String parseStringExpression(String expression, Method method, Object[] args, Object result) {
        Object value = safeParseExpression(expression, method, args, result);
        return value != null ? value.toString() : null;
    }
}
