package com.tem.customer.controller.partner

import cn.dev33.satoken.stp.StpUtil
import com.iplatform.common.ResponseDto
import com.tem.customer.shared.common.ResultCode
import com.tem.customer.shared.exception.BusinessException
import com.tem.customer.shared.utils.UserContextUtil
import com.tem.platform.api.PartnerService
import com.tem.platform.api.dto.PartnerDto
import com.tem.platform.api.dto.UserDto
import com.tem.platform.biz.api.AdminPartnerSearchService
import com.tem.platform.biz.condition.AdminPartnerQueryCondition
import com.tem.platform.biz.enums.AdminTmcPartnerEnum
import org.mockito.Mockito
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification
import spock.lang.Unroll
/**
 * 企业管理控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class PartnerControllerSpec extends Specification {

    @SpringBean
    private PartnerService partnerService = Mock()

    @SpringBean
    private AdminPartnerSearchService adminPartnerSearchService = Mock()

    def partnerController
    def mockStatic
    def mockStpUtil

    def setup() {
        // 设置静态方法Mock
        mockStatic = Mockito.mockStatic(UserContextUtil.class)
        mockStpUtil = Mockito.mockStatic(StpUtil.class)

        // 初始化控制器实例
        partnerController = new PartnerController()

        // 通过反射设置Dubbo服务字段
        def partnerServiceField = PartnerController.class.getDeclaredField("partnerService")
        partnerServiceField.setAccessible(true)
        partnerServiceField.set(partnerController, partnerService)

        def adminPartnerSearchServiceField = PartnerController.class.getDeclaredField("adminPartnerSearchService")
        adminPartnerSearchServiceField.setAccessible(true)
        adminPartnerSearchServiceField.set(partnerController, adminPartnerSearchService)
    }

    def cleanup() {
        // 清理静态方法Mock
        if (mockStatic != null) {
            mockStatic.close()
        }
        if (mockStpUtil != null) {
            mockStpUtil.close()
        }
    }

    def "测试查询企业列表成功"() {
        given: "准备测试数据"
        def partnerDto1 = new PartnerDto()
        partnerDto1.setId(1L)
        partnerDto1.setName("测试企业1")
        partnerDto1.setStatus(0) // 正常状态

        def partnerDto2 = new PartnerDto()
        partnerDto2.setId(2L)
        partnerDto2.setName("测试企业2")
        partnerDto2.setStatus(1) // 已停用状态

        def partnerList = [partnerDto1, partnerDto2]

        def currentUser = new UserDto()
        currentUser.setId(1L)
        currentUser.setPartnerId(100L)

        and: "Mock服务调用"
        mockStatic.when({ UserContextUtil.getCurrentUser() }).thenReturn(currentUser)

        // 修复Mock配置 - 使用ResponseDto.success()方法
        def searchResponse = ResponseDto.success(partnerList)
        adminPartnerSearchService.searchAllPartner(_ as AdminPartnerQueryCondition) >> searchResponse

        and: "Mock Sa-Token相关静态方法"
        mockStpUtil.when({ StpUtil.isLogin() }).thenReturn(true)
        mockStpUtil.when({ StpUtil.getLoginIdAsLong() }).thenReturn(currentUser.getId())

        when: "调用查询企业列表接口"
        def result = partnerController.list(1, 10, "测试", 0)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.list.size() == 2
        result.data.total == 2

        and: "验证已停用企业的名称标记"
        def disabledPartner = result.data.list.find { it.id == 2L }
        disabledPartner.name.startsWith("【已停用】-")
    }

    def "测试查询企业列表失败 - 服务调用失败"() {
        given: "准备测试数据"
        def currentUser = new UserDto()
        currentUser.setId(1L)
        currentUser.setPartnerId(100L)

        and: "Mock服务调用失败"
        mockStatic.when({ UserContextUtil.getCurrentUser() }).thenReturn(currentUser)

        def failedResponse = GroovyMock(ResponseDto)
        failedResponse.isSuccess() >> false
        failedResponse.isNotSuccess() >> true
        failedResponse.getMsg() >> "查询失败"
        adminPartnerSearchService.searchAllPartner(_) >> failedResponse

        and: "Mock Sa-Token相关静态方法"
        mockStpUtil.when({ StpUtil.isLogin() }).thenReturn(true)
        mockStpUtil.when({ StpUtil.getLoginIdAsLong() }).thenReturn(currentUser.getId())

        when: "调用查询企业列表接口"
        def result = partnerController.list(1, 10, "测试", 0)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.list.size() == 0
        result.data.total == 0
    }

    def "测试查询企业列表 - 当前用户为空"() {
        given: "Mock当前用户为空"
        mockStatic.when({ UserContextUtil.getCurrentUser() }).thenReturn(null)

        when: "调用查询企业列表接口"
        partnerController.list(1, 10, "测试", 0)

        then: "验证异常抛出"
        thrown(BusinessException)
    }

    def "测试根据企业ID查询企业详情成功"() {
        given: "准备测试数据"
        def partnerDto1 = new PartnerDto()
        partnerDto1.setId(1L)
        partnerDto1.setName("测试企业1")

        def partnerDto2 = new PartnerDto()
        partnerDto2.setId(2L)
        partnerDto2.setName("测试企业2")

        def partnerList = [partnerDto1, partnerDto2]

        and: "Mock服务调用"
        // 修复Mock配置 - 使用ResponseDto.success()方法
        def findByIdsResponse = ResponseDto.success(partnerList)
        partnerService.findByBpIds([1L, 2L]) >> findByIdsResponse

        when: "调用根据企业ID查询接口"
        def result = partnerController.getPartnersByIds("1,2")

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        result.data[0].id == 1L
        result.data[1].id == 2L
    }

    def "测试根据企业ID查询 - 企业ID为空"() {
        when: "调用根据企业ID查询接口 - 空参数"
        def result = partnerController.getPartnersByIds("")

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 0
    }

    def "测试根据企业ID查询失败 - 服务调用失败"() {
        given: "Mock服务调用失败"
        def failedResponse = GroovyMock(ResponseDto)
        failedResponse.isSuccess() >> false
        failedResponse.getMsg() >> "查询失败"
        partnerService.findByBpIds([1L, 2L]) >> failedResponse

        when: "调用根据企业ID查询接口"
        def result = partnerController.getPartnersByIds("1,2")

        then: "验证返回结果"
        result.code == ResultCode.BUSINESS_ERROR.getCode()
        result.message == "查询失败"
    }

    @Unroll
    def "测试构建查询参数DTO - 企业类型: #partnerType"() {
        given: "准备当前用户信息"
        def currentUser = new UserDto()
        currentUser.setId(1L)
        currentUser.setPartnerId(100L)

        and: "Mock当前用户"
        Mockito.when(UserContextUtil.getCurrentUser()).thenReturn(currentUser)

        when: "调用私有方法构建查询参数"
        def method = PartnerController.class.getDeclaredMethod("buildQueryDTO", String.class, Integer.class)
        method.setAccessible(true)
        def result = method.invoke(partnerController, "测试关键字", partnerType)

        then: "验证查询参数"
        result != null
        result.userId == 1L
        result.tmcId == 100L
        result.keyword == "测试关键字"
        result.partnerType == expectedPartnerType

        where:
        partnerType | expectedPartnerType
        0           | AdminTmcPartnerEnum.PARTNER
        1           | AdminTmcPartnerEnum.TMC
        2           | AdminTmcPartnerEnum.TMC
    }

    def "测试处理企业状态标记"() {
        given: "准备测试数据"
        def partnerDto1 = new PartnerDto()
        partnerDto1.setId(1L)
        partnerDto1.setName("正常企业")
        partnerDto1.setAlias("正常别名")
        partnerDto1.setStatus(0)

        def partnerDto2 = new PartnerDto()
        partnerDto2.setId(2L)
        partnerDto2.setName("停用企业")
        partnerDto2.setAlias("停用别名")
        partnerDto2.setStatus(1)

        def partnerDto3 = new PartnerDto()
        partnerDto3.setId(3L)
        partnerDto3.setName(null)
        partnerDto3.setAlias("停用别名无名称")
        partnerDto3.setStatus(1)

        def partnerList = [partnerDto1, partnerDto2, partnerDto3]

        when: "调用私有方法处理企业状态"
        def method = PartnerController.class.getDeclaredMethod("processPartnerStatus", List.class)
        method.setAccessible(true)
        method.invoke(partnerController, partnerList)

        then: "验证状态标记处理结果"
        partnerList[0].name == "正常企业"
        partnerList[0].alias == "正常别名"
        
        partnerList[1].name == "【已停用】-停用企业"
        partnerList[1].alias == "【已停用】-停用别名"
        
        partnerList[2].name == null
        partnerList[2].alias == "【已停用】-停用别名无名称"
    }

    @Unroll
    def "测试根据关键字过滤企业数据 - 关键字: '#key'"() {
        given: "准备测试数据"
        def partnerDto1 = new PartnerDto()
        partnerDto1.setId(1L)
        partnerDto1.setName("阿里巴巴")

        def partnerDto2 = new PartnerDto()
        partnerDto2.setId(2L)
        partnerDto2.setName("腾讯科技")

        def partnerDto3 = new PartnerDto()
        partnerDto3.setId(3L)
        partnerDto3.setName("百度网络")

        def partnerList = [partnerDto1, partnerDto2, partnerDto3]

        when: "调用私有方法过滤数据"
        def method = PartnerController.class.getDeclaredMethod("filterByKey", List.class, String.class)
        method.setAccessible(true)
        def result = method.invoke(partnerController, partnerList, key)

        then: "验证过滤结果"
        result.size() == expectedSize

        where:
        key     | expectedSize
        null    | 3
        ""      | 3
        " "     | 3
        "阿里"  | 1
        "科技"  | 1
        "网络"  | 1
        "不存在" | 0
    }

    @Unroll
    def "测试创建分页结果 - 页码: #pageNo, 页大小: #pageSize"() {
        given: "准备测试数据"
        def partnerList = []
        for (i in 1..25) {
            def partner = new PartnerDto()
            partner.setId(i as Long)
            partner.setName("企业${i}")
            partnerList.add(partner)
        }

        when: "调用私有方法创建分页结果"
        def method = PartnerController.class.getDeclaredMethod("createPageResult", List.class, Integer.class, Integer.class)
        method.setAccessible(true)
        def result = method.invoke(partnerController, partnerList, pageNo, pageSize)

        then: "验证分页结果"
        result != null
        result.list.size() == expectedPageSize
        result.total == 25

        where:
        pageNo | pageSize | expectedPageSize
        1      | 10       | 10
        2      | 10       | 10
        3      | 10       | 5
        4      | 10       | 0
        1      | 5        | 5
        null   | null     | 10
        0      | 0        | 10  // 修复：当pageSize为0时，会使用默认值10
    }

    def "测试查询企业列表异常处理"() {
        given: "Mock服务调用抛出异常"
        def currentUser = new UserDto()
        currentUser.setId(1L)
        currentUser.setPartnerId(100L)

        mockStatic.when({ UserContextUtil.getCurrentUser() }).thenReturn(currentUser)

        and: "Mock Sa-Token相关静态方法"
        mockStpUtil.when({ StpUtil.isLogin() }).thenReturn(true)
        mockStpUtil.when({ StpUtil.getLoginIdAsLong() }).thenReturn(currentUser.getId())

        adminPartnerSearchService.searchAllPartner(_) >> { throw new RuntimeException("服务异常") }

        when: "调用查询企业列表接口"
        partnerController.list(1, 10, "测试", 0)

        then: "验证异常处理"
        def exception = thrown(BusinessException)
        exception.message.contains("查询企业列表失败")
    }
}