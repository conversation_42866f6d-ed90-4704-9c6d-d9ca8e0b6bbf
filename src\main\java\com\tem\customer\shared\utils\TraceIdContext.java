package com.tem.customer.shared.utils;

import com.tem.customer.shared.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * TraceId上下文工具类
 * 提供TraceId的手动管理和异步任务传递功能
 * <p>
 * 主要功能：
 * 1. TraceId的获取、设置、清理
 * 2. 异步任务的TraceId传递
 * 3. MDC上下文的复制和恢复
 * 4. 定时任务的TraceId生成
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
public class TraceIdContext {

    /**
     * 获取当前TraceId
     *
     * @return 当前TraceId，如果不存在则返回null
     */
    public static String getCurrentTraceId() {
        return MDC.get(Constant.TRACE_ID);
    }

    /**
     * 设置TraceId到MDC
     *
     * @param traceId TraceId
     */
    public static void setTraceId(String traceId) {
        if (StringUtils.isNotBlank(traceId)) {
            MDC.put(Constant.TRACE_ID, traceId.trim());
            if (log.isDebugEnabled()) {
                log.debug("手动设置TraceId: {}", traceId);
            }
        }
    }

    /**
     * 生成并设置新的TraceId
     *
     * @return 生成的TraceId
     */
    public static String generateAndSetTraceId() {
        String traceId = ShortUUID.getUuid();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 生成并设置带时间戳的TraceId（适用于定时任务）
     *
     * @return 生成的TraceId
     */
    public static String generateAndSetTimestampTraceId() {
        String traceId = ShortUUID.getTimestampTraceId();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 生成并设置不含特殊字符的TraceId
     * 只包含字母和数字，避免横线和下划线
     *
     * @return 生成的TraceId
     */
    public static String generateAndSetCleanTraceId() {
        String traceId = ShortUUID.getUuidWithoutSpecialChars();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 生成并设置不含特殊字符的带时间戳TraceId（适用于定时任务）
     * 只包含字母和数字，避免横线和下划线
     *
     * @return 生成的TraceId
     */
    public static String generateAndSetCleanTimestampTraceId() {
        String traceId = ShortUUID.getTimestampTraceIdWithoutSpecialChars();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 清理当前TraceId
     */
    public static void clearTraceId() {
        String traceId = getCurrentTraceId();
        if (StringUtils.isNotBlank(traceId)) {
            MDC.remove(Constant.TRACE_ID);
            if (log.isDebugEnabled()) {
                log.debug("清理TraceId: {}", traceId);
            }
        }
    }

    /**
     * 获取当前MDC的完整副本
     *
     * @return MDC副本
     */
    public static Map<String, String> copyMDC() {
        return MDC.getCopyOfContextMap();
    }

    /**
     * 恢复MDC上下文
     *
     * @param contextMap MDC上下文
     */
    public static void restoreMDC(Map<String, String> contextMap) {
        if (contextMap != null) {
            MDC.setContextMap(contextMap);
        } else {
            MDC.clear();
        }
    }

    /**
     * 包装Runnable任务，自动传递TraceId
     *
     * @param task 原始任务
     * @return 包装后的任务
     */
    public static Runnable wrapWithTraceId(Runnable task) {
        return wrapWithTraceId(task, getCurrentTraceId());
    }

    /**
     * 包装Runnable任务，使用指定的TraceId
     *
     * @param task    原始任务
     * @param traceId 指定的TraceId
     * @return 包装后的任务
     */
    public static Runnable wrapWithTraceId(Runnable task, String traceId) {
        if (task == null) {
            return null;
        }

        // 如果没有TraceId，生成一个新的（不含特殊字符）
        String finalTraceId = StringUtils.isNotBlank(traceId) ? traceId : ShortUUID.getUuidWithoutSpecialChars();

        return new TraceIdRunnable(task, finalTraceId, copyMDC());
    }

    /**
     * 包装Callable任务，自动传递TraceId
     *
     * @param task 原始任务
     * @param <T>  返回值类型
     * @return 包装后的任务
     */
    public static <T> Callable<T> wrapWithTraceId(Callable<T> task) {
        return wrapWithTraceId(task, getCurrentTraceId());
    }

    /**
     * 包装Callable任务，使用指定的TraceId
     *
     * @param task    原始任务
     * @param traceId 指定的TraceId
     * @param <T>     返回值类型
     * @return 包装后的任务
     */
    public static <T> Callable<T> wrapWithTraceId(Callable<T> task, String traceId) {
        if (task == null) {
            return null;
        }

        // 如果没有TraceId，生成一个新的（不含特殊字符）
        String finalTraceId = StringUtils.isNotBlank(traceId) ? traceId : ShortUUID.getUuidWithoutSpecialChars();

        return new TraceIdCallable<>(task, finalTraceId, copyMDC());
    }

    /**
     * 在指定的TraceId上下文中执行任务
     *
     * @param traceId TraceId
     * @param task    任务
     */
    public static void runWithTraceId(String traceId, Runnable task) {
        Map<String, String> originalContext = copyMDC();
        try {
            setTraceId(traceId);
            task.run();
        } finally {
            restoreMDC(originalContext);
        }
    }

    /**
     * 在指定的TraceId上下文中执行任务并返回结果
     *
     * @param traceId TraceId
     * @param task    任务
     * @param <T>     返回值类型
     * @return 任务执行结果
     * @throws Exception 任务执行异常
     */
    public static <T> T callWithTraceId(String traceId, Callable<T> task) throws Exception {
        Map<String, String> originalContext = copyMDC();
        try {
            setTraceId(traceId);
            return task.call();
        } finally {
            restoreMDC(originalContext);
        }
    }

    /**
     * TraceId包装的Runnable实现
     */
    private record TraceIdRunnable(Runnable delegate, String traceId,
                                   Map<String, String> contextMap) implements Runnable {

        @Override
        public void run() {
            Map<String, String> originalContext = copyMDC();
            try {
                // 恢复完整的MDC上下文
                restoreMDC(contextMap);
                // 确保TraceId正确设置
                setTraceId(traceId);

                if (log.isDebugEnabled()) {
                    log.debug("异步任务开始执行，TraceId: {}", traceId);
                }

                delegate.run();
            } catch (Exception e) {
                log.error("异步任务执行失败，TraceId: {}", traceId, e);
                throw e;
            } finally {
                if (log.isDebugEnabled()) {
                    log.debug("异步任务执行完成，TraceId: {}", traceId);
                }
                // 恢复原始上下文
                restoreMDC(originalContext);
            }
        }
    }

    /**
     * TraceId包装的Callable实现
     */
    private record TraceIdCallable<T>(Callable<T> delegate, String traceId,
                                      Map<String, String> contextMap) implements Callable<T> {

        @Override
        public T call() throws Exception {
            Map<String, String> originalContext = copyMDC();
            try {
                // 恢复完整的MDC上下文
                restoreMDC(contextMap);
                // 确保TraceId正确设置
                setTraceId(traceId);

                if (log.isDebugEnabled()) {
                    log.debug("异步任务开始执行，TraceId: {}", traceId);
                }

                return delegate.call();
            } catch (Exception e) {
                log.error("异步任务执行失败，TraceId: {}", traceId, e);
                throw e;
            } finally {
                if (log.isDebugEnabled()) {
                    log.debug("异步任务执行完成，TraceId: {}", traceId);
                }
                // 恢复原始上下文
                restoreMDC(originalContext);
            }
        }
    }
}
