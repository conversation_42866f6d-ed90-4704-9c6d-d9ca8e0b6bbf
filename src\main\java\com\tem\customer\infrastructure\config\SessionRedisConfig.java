package com.tem.customer.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.session.SessionRepository;
import com.tem.customer.infrastructure.session.CustomRedisSessionRepository;
import org.springframework.util.StringUtils;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

/**
 * Session专用Redis配置类
 * 用于配置Spring Session的Redis连接，与业务Redis分离
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@Configuration
@EnableRedisHttpSession()
public class SessionRedisConfig {

    /**
     * Session Redis哨兵节点地址，多个用逗号分隔
     */
    @Value("${cluster.address:}")
    private String clusterAddress;

    /**
     * Session Redis哨兵主节点名称
     */
    @Value("${redis.session.masterName:}")
    private String sessionMasterName;

    /**
     * Session Redis密码
     */
    @Value("${cluster.password:}")
    private String clusterPassword;

    /**
     * Redis连接池配置参数
     */
    @Value("${redis.maxWaitMillis:10000}")
    private long maxWaitMillis;

    @Value("${redis.maxTotal:100}")
    private int maxTotal;

    @Value("${redis.minIdle:10}")
    private int minIdle;

    @Value("${redis.maxIdle:50}")
    private int maxIdle;

    @Value("${redis.testOnBorrow:true}")
    private boolean testOnBorrow;

    /**
     * 配置Jedis连接池
     */
    @Bean("sessionJedisPoolConfig")
    public JedisPoolConfig jedisPoolConfig() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxWait(Duration.ofMillis(maxWaitMillis));
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setTestOnBorrow(testOnBorrow);

        log.info("Session Jedis Pool configured: maxTotal={}, minIdle={}, maxIdle={}, maxWaitMillis={}, testOnBorrow={}",
                maxTotal, minIdle, maxIdle, maxWaitMillis, testOnBorrow);

        return poolConfig;
    }

    /**
     * 配置哨兵配置
     */
    @Bean("sessionSentinelConfig")
    public RedisSentinelConfiguration sessionSentinelConfiguration() {
        if (!StringUtils.hasText(clusterAddress) || !StringUtils.hasText(sessionMasterName)) {
            throw new IllegalArgumentException("Session Redis configuration is missing. " +
                    "Please configure cluster.address and redis.session.masterName");
        }

        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration();
        sentinelConfig.setMaster(sessionMasterName);

        // 解析哨兵节点地址
        Set<String> sentinels = parseSentinelNodes(clusterAddress);
        for (String sentinel : sentinels) {
            String[] parts = sentinel.split(":");
            if (parts.length == 2) {
                sentinelConfig.sentinel(parts[0].trim(), Integer.parseInt(parts[1].trim()));
            }
        }

        if (StringUtils.hasText(clusterPassword)) {
            sentinelConfig.setPassword(clusterPassword);
        }

        log.info("Session Sentinel configured with master: {}, sentinels: {}",
                sessionMasterName, sentinels);

        return sentinelConfig;
    }

    /**
     * 配置Session专用的Redis连接工厂
     * 使用@Primary注解，让Spring Session优先使用这个连接工厂
     */
    @Bean("sessionRedisConnectionFactory")
    @Primary
    public RedisConnectionFactory sessionRedisConnectionFactory(
            @Qualifier("sessionSentinelConfig") RedisSentinelConfiguration sessionSentinelConfig) {

        log.info("Configuring Session Redis Connection Factory with modern Spring Boot 3.5.0 API");

        try {
            // 使用现代的JedisConnectionFactory配置方式
            // 基于Spring Data Redis最新API，使用JedisClientConfiguration
            JedisClientConfiguration clientConfig = JedisClientConfiguration.builder()
                    .usePooling()
                    .poolConfig(jedisPoolConfig())
                    .build();

            JedisConnectionFactory connectionFactory = new JedisConnectionFactory(sessionSentinelConfig, clientConfig);
            connectionFactory.afterPropertiesSet();

            log.info("Session Redis Connection Factory configured successfully with modern API");
            return connectionFactory;
        } catch (Exception e) {
            log.error("Failed to configure Session Redis Connection Factory: {}", e.getMessage(), e);
            throw new IllegalStateException("Unable to configure Session Redis Connection Factory", e);
        }
    }

    /**
     * 配置Session专用的StringRedisTemplate
     */
    @Bean("sessionStringRedisTemplate")
    public StringRedisTemplate sessionStringRedisTemplate(
            @Qualifier("sessionRedisConnectionFactory") RedisConnectionFactory sessionRedisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(sessionRedisConnectionFactory);

        log.info("Session StringRedisTemplate configured");

        return template;
    }

    /**
     * 配置Session序列化器
     * 使用JDK序列化器，保持与统一登录系统的兼容性
     */
    @Bean("sessionRedisSerializer")
    public RedisSerializer<Object> sessionRedisSerializer() {
        return new JdkSerializationRedisSerializer();
    }

    /**
     * 配置自定义的Session仓库，用于处理反序列化异常
     * 使用不同的bean名称避免与Spring Session自动配置冲突
     */
    @Bean("customSessionRepository")
    @Primary
    public SessionRepository<?> customSessionRepository(
            @Qualifier("sessionRedisConnectionFactory") RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);
        redisTemplate.setDefaultSerializer(sessionRedisSerializer());
        redisTemplate.setKeySerializer(RedisSerializer.string());
        redisTemplate.setHashKeySerializer(RedisSerializer.string());
        redisTemplate.afterPropertiesSet();
        
        CustomRedisSessionRepository repository = new CustomRedisSessionRepository(redisTemplate);
        repository.setDefaultMaxInactiveInterval(Duration.ofMinutes(30));
        
        log.info("自定义Session仓库已配置，支持反序列化异常处理");
        
        return repository;
    }

    /**
     * 解析哨兵节点地址
     */
    private Set<String> parseSentinelNodes(String nodes) {
        if (!StringUtils.hasText(nodes)) {
            return new HashSet<>();
        }

        String[] nodeArray = nodes.split(",");
        Set<String> sentinelNodes = new HashSet<>();

        for (String node : nodeArray) {
            String trimmedNode = node.trim();
            if (StringUtils.hasText(trimmedNode)) {
                sentinelNodes.add(trimmedNode);
            }
        }

        return sentinelNodes;
    }
}
