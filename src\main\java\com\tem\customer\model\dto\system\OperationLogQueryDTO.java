package com.tem.customer.model.dto.system;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 操作日志查询DTO
 * 用于接收操作日志查询请求参数
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
public class OperationLogQueryDTO {

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 20;

    /**
     * 业务类型
     */
    @Size(max = 50, message = "业务类型长度不能超过50个字符")
    private String businessType;

    /**
     * 业务ID
     */
    @Min(value = 1, message = "业务ID必须大于0")
    private Long businessId;

    /**
     * 操作类型
     */
    @Size(max = 20, message = "操作类型长度不能超过20个字符")
    private String operationType;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名（模糊查询）
     */
    @Size(max = 100, message = "操作人姓名长度不能超过100个字符")
    private String operatorName;

    /**
     * 操作人用户名（模糊查询）
     */
    @Size(max = 100, message = "操作人用户名长度不能超过100个字符")
    private String operatorUsername;

    /**
     * 操作人所属企业ID
     */
    private Long partnerId;

    /**
     * 目标企业ID
     */
    private Long targetPartnerId;

    /**
     * 操作描述（模糊查询）
     */
    private String operationDesc;

    /**
     * 请求URI（模糊查询）
     */
    private String requestUri;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最小执行时间（毫秒）
     */
    private Integer minExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    private Integer maxExecutionTime;

    /**
     * 排序字段，默认按创建时间倒序
     */
    private String orderBy = "create_time";

    /**
     * 排序方向，ASC或DESC，默认DESC
     */
    private String orderDirection = "DESC";
}
