package com.tem.customer.controller.partner

import com.tem.customer.BaseControllerSpec
import com.tem.customer.model.convert.PartnerWechatGroupConverter
import com.tem.customer.model.dto.partner.PartnerWechatGroupDTO
import com.tem.customer.model.vo.partner.PartnerWechatGroupVO
import com.tem.customer.repository.entity.PartnerWechatGroup
import com.tem.customer.service.partner.PartnerWechatGroupService
import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDateTime

/**
 * 企业微信群绑定关系控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
class PartnerWechatGroupControllerSpec extends BaseControllerSpec {

    @Subject
    PartnerWechatGroupController partnerWechatGroupController

    @MockitoBean
    PartnerWechatGroupService partnerWechatGroupService = Mock()

    def setup() {
        partnerWechatGroupController = new PartnerWechatGroupController(partnerWechatGroupService)
    }

    def "测试根据企业ID查询微信群列表 - 正常查询"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def group1 = createPartnerWechatGroup(1L, partnerId, "chat001", "CUSTOMER_SERVICE", 1, 1)
        def group2 = createPartnerWechatGroup(2L, partnerId, "chat002", "CUSTOMER_SERVICE", 1, 2)
        def groups = [group1, group2]

        and: "Mock服务调用"
        partnerWechatGroupService.listByPartnerId(partnerId) >> groups

        when: "调用查询接口"
        def result = partnerWechatGroupController.listByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        result.data[0].id == 1L
        result.data[0].chatId == "chat001"
        result.data[1].id == 2L
        result.data[1].chatId == "chat002"
    }

    def "测试根据企业ID查询微信群列表 - 空列表"() {
        given: "准备测试数据"
        def partnerId = 1001L

        and: "Mock服务调用返回空列表"
        partnerWechatGroupService.listByPartnerId(partnerId) >> []

        when: "调用查询接口"
        def result = partnerWechatGroupController.listByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.isEmpty()
    }

    def "测试根据企业ID查询启用状态的微信群列表"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def group = createPartnerWechatGroup(1L, partnerId, "chat001", "CUSTOMER_SERVICE", 1, 1)
        def groups = [group]

        and: "Mock服务调用"
        partnerWechatGroupService.listEnabledByPartnerId(partnerId) >> groups

        when: "调用查询接口"
        def result = partnerWechatGroupController.listEnabledByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 1
        result.data[0].id == 1L
        result.data[0].status == 1
    }

    def "测试根据ID查询微信群详情 - 存在记录"() {
        given: "准备测试数据"
        def id = 1L
        def group = createPartnerWechatGroup(id, 1001L, "chat001", "CUSTOMER_SERVICE", 1, 1)

        and: "Mock服务调用"
        partnerWechatGroupService.getById(id) >> group

        when: "调用查询接口"
        def result = partnerWechatGroupController.getById(id)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.id == id
        result.data.chatId == "chat001"
    }

    def "测试根据ID查询微信群详情 - 记录不存在"() {
        given: "准备测试数据"
        def id = 999L

        and: "Mock服务调用返回null"
        partnerWechatGroupService.getById(id) >> null

        when: "调用查询接口"
        def result = partnerWechatGroupController.getById(id)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message != null
    }

    def "测试根据chatId查询微信群详情 - 存在记录"() {
        given: "准备测试数据"
        def chatId = "chat001"
        def vo = createPartnerWechatGroupVO(1L, 1001L, chatId, "CUSTOMER_SERVICE", 1, 1)

        and: "Mock服务调用"
        partnerWechatGroupService.getByChatIdVO(chatId) >> vo

        when: "调用查询接口"
        def result = partnerWechatGroupController.getByChatId(chatId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.chatId == chatId
    }

    def "测试根据chatId查询微信群详情 - 记录不存在"() {
        given: "准备测试数据"
        def chatId = "nonexistent"

        and: "Mock服务调用返回null"
        partnerWechatGroupService.getByChatIdVO(chatId) >> null

        when: "调用查询接口"
        def result = partnerWechatGroupController.getByChatId(chatId)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message != null
    }

    def "测试创建企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def dto = createPartnerWechatGroupDTO(null, 1001L, "chat001", "CUSTOMER_SERVICE", 1, 1)
        def group = createPartnerWechatGroup(1L, 1001L, "chat001", "CUSTOMER_SERVICE", 1, 1)

        and: "Mock服务调用"
        partnerWechatGroupService.createPartnerWechatGroup(_) >> group

        when: "调用创建接口"
        def result = partnerWechatGroupController.create(dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.id == 1L
        result.data.chatId == "chat001"
    }

    def "测试创建企业微信群绑定关系 - 失败"() {
        given: "准备测试数据"
        def dto = createPartnerWechatGroupDTO(null, 1001L, "chat001", "CUSTOMER_SERVICE", 1, 1)

        and: "Mock服务调用返回null"
        partnerWechatGroupService.createPartnerWechatGroup(_) >> null

        when: "调用创建接口"
        def result = partnerWechatGroupController.create(dto)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "创建失败"
    }

    def "测试更新企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def dto = createPartnerWechatGroupDTO(id, 1001L, "chat001", "CUSTOMER_SERVICE", 1, 1)

        and: "Mock服务调用"
        partnerWechatGroupService.updatePartnerWechatGroup(_) >> true

        when: "调用更新接口"
        def result = partnerWechatGroupController.update(id, dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        
        and: "验证DTO的ID被正确设置"
        dto.id == id
    }

    def "测试更新企业微信群绑定关系 - 失败"() {
        given: "准备测试数据"
        def id = 1L
        def dto = createPartnerWechatGroupDTO(id, 1001L, "chat001", "CUSTOMER_SERVICE", 1, 1)

        and: "Mock服务调用返回false"
        partnerWechatGroupService.updatePartnerWechatGroup(_) >> false

        when: "调用更新接口"
        def result = partnerWechatGroupController.update(id, dto)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "更新失败"
    }

    def "测试删除企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L

        and: "Mock服务调用"
        partnerWechatGroupService.deletePartnerWechatGroup(id) >> true

        when: "调用删除接口"
        def result = partnerWechatGroupController.delete(id)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
    }

    def "测试删除企业微信群绑定关系 - 失败"() {
        given: "准备测试数据"
        def id = 1L

        and: "Mock服务调用返回false"
        partnerWechatGroupService.deletePartnerWechatGroup(id) >> false

        when: "调用删除接口"
        def result = partnerWechatGroupController.delete(id)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "删除失败"
    }

    def "测试检查chatId是否已存在 - 存在"() {
        given: "准备测试数据"
        def chatId = "chat001"
        def excludeId = null

        and: "Mock服务调用"
        partnerWechatGroupService.existsByChatId(chatId, excludeId) >> true

        when: "调用检查接口"
        def result = partnerWechatGroupController.checkChatIdExists(chatId, excludeId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == true
    }

    def "测试检查chatId是否已存在 - 不存在"() {
        given: "准备测试数据"
        def chatId = "chat999"
        def excludeId = 1L

        and: "Mock服务调用"
        partnerWechatGroupService.existsByChatId(chatId, excludeId) >> false

        when: "调用检查接口"
        def result = partnerWechatGroupController.checkChatIdExists(chatId, excludeId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == false
    }

    // ==================== 辅助方法 ====================

    private PartnerWechatGroup createPartnerWechatGroup(Long id, Long partnerId, String chatId, 
                                                        String groupType, Integer status, Integer sortOrder) {
        def group = new PartnerWechatGroup()
        group.setId(id)
        group.setPartnerId(partnerId)
        group.setChatId(chatId)
        group.setGroupType(groupType)
        group.setStatus(status)
        group.setSortOrder(sortOrder)
        group.setCreateTime(LocalDateTime.now())
        group.setUpdateTime(LocalDateTime.now())
        return group
    }

    private PartnerWechatGroupVO createPartnerWechatGroupVO(Long id, Long partnerId, String chatId,
                                                           String groupType, Integer status, Integer sortOrder) {
        def vo = new PartnerWechatGroupVO()
        vo.setId(id)
        vo.setPartnerId(partnerId)
        vo.setChatId(chatId)
        vo.setGroupType(groupType)
        vo.setStatus(status)
        vo.setSortOrder(sortOrder)
        vo.setCreateTime(LocalDateTime.now())
        vo.setUpdateTime(LocalDateTime.now())
        return vo
    }

    private PartnerWechatGroupDTO createPartnerWechatGroupDTO(Long id, Long partnerId, String chatId,
                                                             String groupType, Integer status, Integer sortOrder) {
        def dto = new PartnerWechatGroupDTO()
        dto.setId(id)
        dto.setPartnerId(partnerId)
        dto.setChatId(chatId)
        dto.setGroupType(groupType)
        dto.setStatus(status)
        dto.setSortOrder(sortOrder)
        return dto
    }
}
