package com.tem.customer.model.dto.qiyu;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 七鱼企微客服群聊获取群信息请求对象
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QiyuGroupInfoRequest extends QiyuCrmRequest {

    /**
     * 企微群聊ID
     */
    private String chatId;

    /**
     * 来源类型（wx_cs:企微客服渠道）
     */
    private String fromType;

    /**
     * 企微助手id
     */
    private String wxworkUserId;

    /**
     * 企微助手名字
     */
    private String wxworkUserName;

    /**
     * 验证企微群聊获取群信息的必要参数
     * 
     * @return 验证结果
     */
    public boolean validateGroupInfoParams() {
        return chatId != null && !chatId.trim().isEmpty()
            && fromType != null && !fromType.trim().isEmpty();
    }
}
