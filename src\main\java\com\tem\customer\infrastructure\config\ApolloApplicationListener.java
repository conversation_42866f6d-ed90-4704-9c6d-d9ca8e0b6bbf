package com.tem.customer.infrastructure.config;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.aliyun.openservices.log.logback.LoghubAppender;
import com.iplatform.common.Config;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.GenericApplicationListener;
import org.springframework.core.ResolvableType;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
@Data
public class ApolloApplicationListener implements GenericApplicationListener {

    private static final Class<?>[] EVENT_TYPES = { ApplicationEnvironmentPreparedEvent.class };

    private String name = "ApolloApplicationListener";

    @Override
    public boolean supportsEventType(ResolvableType eventType) {
        return isAssignableFrom(eventType.getRawClass(), EVENT_TYPES);
    }

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ApplicationEnvironmentPreparedEvent) {
            log.info("apollo config ready");
            addLoghubAppender();
        }
    }

    private boolean isAssignableFrom(Class<?> type, Class<?>... supportedTypes) {
        if (type != null) {
            for (Class<?> supportedType : supportedTypes) {
                if (supportedType.isAssignableFrom(type)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void addLoghubAppender() {
        log.info("Add AliyunLog Appender");
        // 获取Logger上下文
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();

        // 创建Appender
        LoghubAppender<ILoggingEvent> loghubAppender = new LoghubAppender<>();
        loghubAppender.setContext(context);
        loghubAppender.setName("aliyunLog");
        loghubAppender.setTimeFormat(Config.getString("log4j.appender.aliyunlog.timeFormat"));
        loghubAppender.setTimeZone(Config.getString("log4j.appender.aliyunlog.timeZone"));
        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
        encoder.setContext(context);
        encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n%mdc{traceId}");
        encoder.start();
        loghubAppender.setEncoder(encoder);

        // 设置必要的配置项
        loghubAppender.setEndpoint(Config.getString("log4j.appender.aliyunlog.endpoint"));
        loghubAppender.setAccessKeyId(Config.getString("log4j.appender.aliyunlog.accessKeyId"));
        loghubAppender.setAccessKeySecret(Config.getString("log4j.appender.aliyunlog.accessKeySecret"));
        loghubAppender.setProject(Config.getString("log4j.appender.aliyunlog.project"));
        loghubAppender.setLogStore(Config.getString("log4j.appender.aliyunlog.logStore"));
        //loghubAppender.setMdcFields("traceId,location,lang,trafficTag,currentAppTagDesc");
        loghubAppender.setMdcFields("*");

        // 设置其他配置项
        loghubAppender.setTopic("customer-admin-web");
        //# 单个生产者实例可以容纳的日志大小上限，默认为 100MB。
        loghubAppender.setTotalSizeInBytes(Config.getInt("log4j.appender.aliyunlog.totalSizeInBytes"));
        //#如果生产者可用空间不足，调用者在send方法上的最大阻塞时间，默认为60秒。 为了不阻塞日志打印线程，强烈建议将此值设置为0。
        loghubAppender.setMaxBlockMs(Config.getInt("log4j.appender.aliyunlog.maxBlockMs"));
        //#执行日志发送任务的线程池大小，默认为可用处理器数量。
        loghubAppender.setIoThreadCount(Config.getInt("log4j.appender.loghub.ioThreadCount"));
        //# 当生产者中缓存的日志大小大于等于 batchSizeThresholdInBytes 时，将发送该批次，默认为 512KB，最大可设置为 5MB。
        loghubAppender.setBatchSizeThresholdInBytes(Config.getInt("log4j.appender.aliyunlog.batchSizeThresholdInBytes"));
        //# 当缓存的日志条目数大于或等于 batchCountThreshold 时，将发送该批次。
        loghubAppender.setBatchCountThreshold(Config.getInt("log4j.appender.aliyunlog.batchCountThreshold"));
        //# 从创建到发送有一个停留时间，默认为 2 秒，最小为 100 毫秒。
        loghubAppender.setBatchCountThreshold(Config.getInt("log4j.appender.aliyunlog.lingerMs"));
        //# 发送失败重试次数
        loghubAppender.setRetries(Config.getInt("log4j.appender.aliyunlog.retries"));
        //# 第一次重试的退避时间，默认100毫秒。
        loghubAppender.setBaseRetryBackoffMs(Config.getInt("log4j.appender.aliyunlog.baseRetryBackoffMs"));
        //# 重试的最大退避时间，默认为 50 秒。
        loghubAppender.setMaxRetryBackoffMs(Config.getInt("log4j.appender.aliyunlog.maxRetryBackoffMs"));
        // 启动Appender
        loghubAppender.start();

        // 获取根Logger
        Logger rootLogger = context.getLogger(Logger.ROOT_LOGGER_NAME);

        // 将Appender添加到Logger
        rootLogger.addAppender(loghubAppender);
    }
}
