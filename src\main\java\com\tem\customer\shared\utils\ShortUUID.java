package com.tem.customer.shared.utils;

import java.util.UUID;

/**
 * 22位短UUID字符串生成类
 * 线程安全的UUID生成工具，适用于分布式追踪场景
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public class ShortUUID {

    /**
     * Base64编码字符集（不包含特殊字符，避免URL编码问题）
     */
    private static final char[] BASE_64_ENCODE_CHARS = {
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
            'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
            'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '-', '_'
    };

    /**
     * 标准短UUID长度
     */
    private static final int STANDARD_UUID_LENGTH = 22;

    /**
     * 时间戳部分长度
     */
    private static final int TIMESTAMP_PART_LENGTH = 8;

    /**
     * UUID部分长度（用于带时间戳的TraceId）
     */
    private static final int UUID_PART_LENGTH = 14;

    /**
     * 字节转换时的掩码
     */
    private static final int BYTE_MASK = 0xff;

    /**
     * 长整型字节数组长度
     */
    private static final int LONG_BYTE_LENGTH = 8;

    /**
     * UUID字节数组长度
     */
    private static final int UUID_BYTE_LENGTH = 16;

    /**
     * 调整字符串到指定长度
     * 如果超过长度则截断，如果不足则用编码表第一个字符填充
     *
     * @param str          待调整的字符串
     * @param targetLength 目标长度
     * @return 调整后的字符串
     */
    private static String adjustToLength(String str, int targetLength) {
        if (str == null) {
            str = "";
        }

        if (str.length() > targetLength) {
            return str.substring(0, targetLength);
        } else if (str.length() < targetLength) {
            StringBuilder paddedResult = new StringBuilder(str);
            while (paddedResult.length() < targetLength) {
                paddedResult.append(BASE_64_ENCODE_CHARS[0]);
            }
            return paddedResult.toString();
        }

        return str;
    }

    /**
     * 将长整型转换为字节数组
     * 使用大端字节序（高位字节在前）
     *
     * @param value 待转换的长整型值
     * @return 8字节的字节数组
     */
    private static byte[] longToBytes(long value) {
        byte[] byteArray = new byte[LONG_BYTE_LENGTH];
        for (int i = LONG_BYTE_LENGTH - 1; i >= 0; i--) {
            byteArray[i] = (byte) (value & BYTE_MASK);
            value >>>= 8;
        }
        return byteArray;
    }

    /**
     * URL安全的Base64编码器
     * 将字节数组编码为Base64字符串的内部实现
     */
    private static class UrlSafeBase64Encoder {

        /**
         * 对字节数组进行Base64编码
         *
         * @param inputData 待编码的字节数组
         * @return 编码后的字符串
         */
        static String encodeToString(byte[] inputData) {
            if (inputData == null || inputData.length == 0) {
                return "";
            }

            StringBuilder encodedResult = new StringBuilder();
            int dataLength = inputData.length;
            int index = 0;

            while (index < dataLength) {
                int firstByte = inputData[index++] & BYTE_MASK;
                if (index == dataLength) {
                    encodedResult.append(BASE_64_ENCODE_CHARS[firstByte >>> 2]);
                    encodedResult.append(BASE_64_ENCODE_CHARS[(firstByte & 0x3) << 4]);
                    break;
                }

                int secondByte = inputData[index++] & BYTE_MASK;
                int combinedIndex = ((firstByte & 0x03) << 4) | ((secondByte & 0xf0) >>> 4);
                if (index == dataLength) {
                    encodedResult.append(BASE_64_ENCODE_CHARS[firstByte >>> 2]);
                    encodedResult.append(BASE_64_ENCODE_CHARS[combinedIndex]);
                    encodedResult.append(BASE_64_ENCODE_CHARS[(secondByte & 0x0f) << 2]);
                    break;
                }

                int thirdByte = inputData[index++] & BYTE_MASK;
                encodedResult.append(BASE_64_ENCODE_CHARS[firstByte >>> 2]);
                encodedResult.append(BASE_64_ENCODE_CHARS[combinedIndex]);
                encodedResult.append(BASE_64_ENCODE_CHARS[((secondByte & 0x0f) << 2) | ((thirdByte & 0xc0) >>> 6)]);
                encodedResult.append(BASE_64_ENCODE_CHARS[thirdByte & 0x3f]);
            }

            return encodedResult.toString();
        }
    }

    /**
     * 将字节数组编码为Base64字符串（线程安全版本）
     * 确保返回固定长度的22位字符串
     *
     * @param data 待编码的字节数组
     * @return 22位Base64编码字符串
     */
    private static String encode(byte[] data) {
        String encodedString = UrlSafeBase64Encoder.encodeToString(data);
        return adjustToLength(encodedString, STANDARD_UUID_LENGTH);
    }

    /**
     * 获取UUID的字节数组表示
     * 使用统一的字节转换方法，确保转换的一致性
     *
     * @return 16字节的UUID字节数组
     */
    private static byte[] getUuidBytes() {
        UUID uuid = UUID.randomUUID();
        long mostSignificantBits = uuid.getMostSignificantBits();
        long leastSignificantBits = uuid.getLeastSignificantBits();

        byte[] uuidByteArray = new byte[UUID_BYTE_LENGTH];
        byte[] msbBytes = longToBytes(mostSignificantBits);
        byte[] lsbBytes = longToBytes(leastSignificantBits);

        // 将高位和低位字节合并
        System.arraycopy(msbBytes, 0, uuidByteArray, 0, LONG_BYTE_LENGTH);
        System.arraycopy(lsbBytes, 0, uuidByteArray, LONG_BYTE_LENGTH, LONG_BYTE_LENGTH);

        return uuidByteArray;
    }

    /**
     * 生成22位短UUID字符串
     * 线程安全，适用于高并发场景
     *
     * @return 22位短UUID字符串
     */
    public static String getUuid() {
        byte[] data = getUuidBytes();
        return encode(data);
    }

    /**
     * 生成带时间戳前缀的TraceId
     * 格式：时间戳(8位) + 短UUID(14位) = 22位
     * 便于按时间排序和查询
     *
     * @return 带时间戳的TraceId
     */
    public static String getTimestampTraceId() {
        long currentTimestamp = System.currentTimeMillis();

        // 使用统一的字节转换方法
        byte[] timestampBytes = longToBytes(currentTimestamp);
        String timestampEncoded = encode(timestampBytes);
        String shortUuidString = getUuid();

        // 取时间戳编码的指定位数和UUID的指定位数
        String timestampPart = adjustToLength(timestampEncoded, TIMESTAMP_PART_LENGTH);
        if (timestampEncoded.length() >= TIMESTAMP_PART_LENGTH) {
            timestampPart = timestampEncoded.substring(0, TIMESTAMP_PART_LENGTH);
        }

        String uuidPart = adjustToLength(shortUuidString, UUID_PART_LENGTH);
        if (shortUuidString.length() >= UUID_PART_LENGTH) {
            uuidPart = shortUuidString.substring(0, UUID_PART_LENGTH);
        }

        String combinedResult = timestampPart + uuidPart;

        // 确保总长度为标准长度
        return adjustToLength(combinedResult, STANDARD_UUID_LENGTH);
    }

    /**
     * 生成不包含横线和下划线的22位短UUID字符串
     * 只使用字母和数字，避免特殊字符
     *
     * @return 22位不含特殊字符的UUID字符串
     */
    public static String getUuidWithoutSpecialChars() {
        String uuid = getUuid();
        // 将横线和下划线替换为字母数字字符
        return uuid.replace('-', 'X').replace('_', 'Y');
    }

    /**
     * 生成不包含横线和下划线的带时间戳TraceId
     * 只使用字母和数字，避免特殊字符
     *
     * @return 22位不含特殊字符的带时间戳TraceId
     */
    public static String getTimestampTraceIdWithoutSpecialChars() {
        String traceId = getTimestampTraceId();
        // 将横线和下划线替换为字母数字字符
        return traceId.replace('-', 'X').replace('_', 'Y');
    }

    /**
     * 验证TraceId格式是否有效
     *
     * @param traceId 待验证的TraceId
     * @return 是否有效
     */
    public static boolean isValidTraceId(String traceId) {
        if (traceId == null || traceId.trim().isEmpty()) {
            return false;
        }

        String trimmed = traceId.trim();
        // 长度检查：22位或更短
        if (trimmed.length() > 22) {
            return false;
        }

        // 字符检查：只允许URL安全的Base64字符（字母、数字、连字符、下划线）
        return trimmed.matches("^[A-Za-z0-9\\-_]+$");
    }
}

