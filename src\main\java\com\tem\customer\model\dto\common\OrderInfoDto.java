package com.tem.customer.model.dto.common;

import com.iplatform.common.utils.BigDecimalUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderInfoDto {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 业务类型，10-机票/11-酒店/12-火车/13-国际酒店/14-需求单/15-保险/16-用车/17-国际机票/18-国际火车/19-国际用车等(来源于订单号前两位)
     */
    private String bizType;

    /**
     * 客户应付订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 业务系统显示状态，来源于业务系统
     */
    private String orderShowStatus;

    /**
     * 酒店名称,
     */
    private String hotelName;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 行程
     */
    private String trip;

    /**
     * 保险产品名称
     */
    private String insuranceName;

    /**
     * 服务品类名称
     */
    private String serviceName;

    /**
     * 行程开始时间
     */
    private Date travelStartTime;

    /**
     * 乘客用户姓名
     */
    private String userName;

    /**
     * 入住时间
     */
    private Integer stayDays;

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = BigDecimalUtils.divide(new BigDecimal(totalAmount), 100);
    }

}





