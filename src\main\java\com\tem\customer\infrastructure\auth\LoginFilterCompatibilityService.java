package com.tem.customer.infrastructure.auth;

import cn.dev33.satoken.stp.StpUtil;
import com.iplatform.common.Config;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.SpringContextUtils;
import com.iplatform.common.transform.TransformUtils;
import com.iplatform.common.utils.DigestUtils;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.common.Constant;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.UserDto;
import com.tem.platform.security.authorize.Constants;
import com.tem.platform.security.authorize.ContextHolder;
import com.tem.platform.security.authorize.ContextImpl;
import com.tem.platform.security.authorize.Org;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * LoginFilter兼容性服务
 * 提供与原有LoginFilter相同的认证逻辑，并与Sa-Token集成
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class LoginFilterCompatibilityService {

    /**
     * 密码加密盐值（与LoginFilter保持一致）
     */
    private static final String PASSWORD_SALT = "$TANREY";

    /**
     * 用户名参数名
     */
    private static final String PARAM_USERNAME = "u";

    /**
     * 密码参数名
     */
    private static final String PARAM_PASSWORD = "p";

    /**
     * 尝试LoginFilter兼容认证
     *
     * @param request HTTP请求
     * @return 认证是否成功
     */
    public boolean tryCompatibleAuthentication(HttpServletRequest request) {
        try {
            // 1. 处理本地测试登录（兼容LoginFilter的URL参数登录）
            if (handleLocalTestLogin(request)) {
                return true;
            }

            // 2. 验证现有Session（兼容LoginFilter的Session验证）
            if (validateExistingSession(request)) {
                return true;
            }

             return false;

        } catch (Exception e) {
            LogUtils.warn(log, "LoginFilter兼容认证异常", e);
            return false;
        }
    }

    /**
     * 处理本地测试登录（兼容LoginFilter逻辑）
     *
     * @param request HTTP请求
     * @return 登录是否成功
     */
    private boolean handleLocalTestLogin(HttpServletRequest request) {
        String userName = request.getParameter(PARAM_USERNAME);
        String password = request.getParameter(PARAM_PASSWORD);

        if (StringUtils.isBlank(userName) || StringUtils.isBlank(password)) {
            return false;
        }

        try {
            UserService userService = SpringContextUtils.getBean(UserService.class);
            String encryptedPassword = DigestUtils.md5(password + PASSWORD_SALT);
            ResponseDto<UserDto> userResponse = userService.getByUsernameAndPassword(userName, encryptedPassword);

            if (userResponse.isNotSuccess() || userResponse.getData() == null) {
                LogUtils.warn(log, "本地测试登录失败: username={}", userName);
                return false;
            }

            UserDto userDto = userResponse.getData();

            // 登录到Sa-Token
            StpUtil.login(userDto.getId());

            // 兼容原有Session设置
            createCompatibleUserSession(request, userDto);

            LogUtils.info(log, "本地测试登录成功: userId={}, username={}", userDto.getId(), userName);
            return true;

        } catch (Exception e) {
            LogUtils.error(log, "本地测试登录处理异常: username=" + userName, e);
            return false;
        }
    }

    /**
     * 验证现有Session（兼容LoginFilter的Session验证逻辑）
     *
     * @param request HTTP请求
     * @return Session是否有效
     */
    private boolean validateExistingSession(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session == null) {
                return false;
            }

            Object userSession = session.getAttribute(Constants.USER_SESSION_KEY);
            if (userSession == null) {
                return false;
            }

            // 检查会话超时
            if (isSessionTimeout(session)) {
                LogUtils.info(log, "Session已超时，清理会话");
                session.invalidate();
                return false;
            }

            // 重新设置ContextHolder到当前线程（修复Cookie登录时API调用获取不到用户信息的问题）
            if (userSession instanceof com.tem.platform.security.authorize.User user) {
                ContextHolder.setContext(new ContextImpl(user));
                LogUtils.debug(log, "重新设置ContextHolder成功: userId={}", user.getId());
            } else {
                LogUtils.warn(log, "Session中的用户对象类型不正确: {}", userSession.getClass().getName());
            }

            return true;

        } catch (org.springframework.data.redis.serializer.SerializationException e) {
            // 处理序列化异常：清理Session并记录日志
            LogUtils.warn(log, "检测到Session序列化异常，清理Session: {}", e.getMessage());
            try {
                HttpSession session = request.getSession(false);
                if (session != null) {
                    session.invalidate();
                }
            } catch (Exception cleanupException) {
                LogUtils.error(log, "清理Session时发生异常", cleanupException);
            }
            return false;
        } catch (Exception e) {
            LogUtils.warn(log, "Session验证异常", e);
            return false;
        }
    }

    /**
     * 创建兼容的用户会话（兼容LoginFilter的Session设置）
     *
     * @param request HTTP请求
     * @param userDto 用户信息
     */
    private void createCompatibleUserSession(HttpServletRequest request, UserDto userDto) {
        try {
            // 创建用户对象（兼容原有逻辑）
            com.tem.platform.security.authorize.User user = new com.tem.platform.security.authorize.User();
            user.setOrg(new Org());
            TransformUtils.transform(userDto, user);

            // 设置ContextHolder（兼容原有逻辑）
            ContextHolder.setContext(new ContextImpl(user));

            // 设置Session属性（兼容原有逻辑）
            HttpSession session = request.getSession();
            session.setAttribute(Constants.USER_SESSION_KEY, user);
            session.setAttribute("userId", user.getId());
            session.setAttribute(Constant.Login.LOGIN_TIME, LocalDateTime.now());

            LogUtils.debug(log, "兼容用户会话创建成功: userId={}", userDto.getId());

        } catch (Exception e) {
            LogUtils.error(log, "创建兼容用户会话失败: userId=" + userDto.getId(), e);
        }
    }

    /**
     * 检查会话是否超时（兼容LoginFilter的超时逻辑）
     *
     * @param session HTTP会话
     * @return 是否超时
     */
    private boolean isSessionTimeout(HttpSession session) {
        try {
            LocalDateTime loginTime = (LocalDateTime) session.getAttribute(Constant.Login.LOGIN_TIME);
            if (loginTime == null) {
                return false;
            }

            Integer maxIntervalHours = Optional.ofNullable(Config.getInt(Constant.Login.LOGIN_MOST_INTERVAL_HOUR))
                    .orElse(24);

            LocalDateTime expireTime = LocalDateTime.now().minusHours(maxIntervalHours);
            return loginTime.isBefore(expireTime);

        } catch (org.springframework.data.redis.serializer.SerializationException e) {
            LogUtils.warn(log, "检查会话超时时发生序列化异常: {}", e.getMessage());
            return true; // 序列化异常情况下认为超时，触发Session清理
        } catch (Exception e) {
            LogUtils.warn(log, "检查会话超时异常", e);
            return true; // 异常情况下认为超时
        }
    }

    /**
     * 检查是否为排除URL（兼容LoginFilter的排除逻辑）
     *
     * @param request HTTP请求
     * @return 是否为排除URL
     */
    public boolean isExcludedUrl(HttpServletRequest request) {
        String ki4soExcludeUrl = Config.getString("ki4soExcludeUrl");
        if (StringUtils.isBlank(ki4soExcludeUrl)) {
            return false;
        }

        String uri = request.getRequestURI();
        String[] excludeUrls = ki4soExcludeUrl.split(",");

        for (String excludeUrl : excludeUrls) {
            if (StringUtils.isNotBlank(excludeUrl) && uri.contains(excludeUrl.trim())) {
                return true;
            }
        }

        return false;
    }
}
