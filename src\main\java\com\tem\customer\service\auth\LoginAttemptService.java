package com.tem.customer.service.auth;

import com.iplatform.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 登录尝试管理服务
 * 用于管理用户登录失败次数和锁定状态
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class LoginAttemptService {

    /**
     * 最大登录失败次数
     */
    private static final int MAX_FAILED_ATTEMPTS = 10;

    /**
     * 锁定时间（2小时）
     */
    private static final Duration LOCK_DURATION = Duration.ofHours(1);

    /**
     * Redis key前缀 - 登录失败次数
     */
    private static final String FAILED_ATTEMPTS_KEY_PREFIX = "login:failed_attempts:";

    /**
     * Redis key前缀 - 账户锁定
     */
    private static final String ACCOUNT_LOCKED_KEY_PREFIX = "login:account_locked:";

    private final RedissonClient redissonClient;

    public LoginAttemptService(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 记录登录失败
     *
     * @param mobile 手机号
     */
    public void recordLoginFailure(String mobile) {
        try {
            String failedAttemptsKey = FAILED_ATTEMPTS_KEY_PREFIX + mobile;

            // 获取当前失败次数
            RBucket<String> bucket = redissonClient.getBucket(failedAttemptsKey);
            String currentAttemptsStr = bucket.get();
            int currentAttempts = currentAttemptsStr != null ? Integer.parseInt(currentAttemptsStr) : 0;

            // 增加失败次数
            currentAttempts++;

            if (currentAttempts >= MAX_FAILED_ATTEMPTS) {
                // 达到最大失败次数，锁定账户
                lockAccount(mobile);
                LogUtils.warn(log, "用户账户已锁定: mobile={}, failedAttempts={}", mobile, currentAttempts);
            } else {
                // 更新失败次数，设置过期时间为锁定时间
                bucket.set(String.valueOf(currentAttempts), LOCK_DURATION);
                LogUtils.info(log, "记录登录失败: mobile={}, failedAttempts={}", mobile, currentAttempts);
            }

        } catch (Exception e) {
            LogUtils.error(log, "记录登录失败异常: mobile=" + mobile, e);
        }
    }

    /**
     * 清除登录失败记录
     *
     * @param mobile 手机号
     */
    public void clearLoginFailures(String mobile) {
        try {
            String failedAttemptsKey = FAILED_ATTEMPTS_KEY_PREFIX + mobile;
            String accountLockedKey = ACCOUNT_LOCKED_KEY_PREFIX + mobile;

            redissonClient.getBucket(failedAttemptsKey).delete();
            redissonClient.getBucket(accountLockedKey).delete();

            LogUtils.info(log, "清除登录失败记录: mobile={}", mobile);

        } catch (Exception e) {
            LogUtils.error(log, "清除登录失败记录异常: mobile=" + mobile, e);
        }
    }

    /**
     * 检查账户是否被锁定
     *
     * @param mobile 手机号
     * @return true-已锁定，false-未锁定
     */
    public boolean isAccountLocked(String mobile) {
        try {
            String accountLockedKey = ACCOUNT_LOCKED_KEY_PREFIX + mobile;
            RBucket<String> bucket = redissonClient.getBucket(accountLockedKey);
            boolean locked = bucket.isExists();

            if (locked) {
                LogUtils.warn(log, "账户已锁定: mobile={}", mobile);
                return true;
            }

            return false;

        } catch (Exception e) {
            LogUtils.error(log, "检查账户锁定状态异常: mobile=" + mobile, e);
            // 异常情况下为了安全考虑，返回锁定状态
            return true;
        }
    }

    /**
     * 获取当前失败次数
     *
     * @param mobile 手机号
     * @return 失败次数
     */
    public int getFailedAttempts(String mobile) {
        try {
            String failedAttemptsKey = FAILED_ATTEMPTS_KEY_PREFIX + mobile;
            RBucket<String> bucket = redissonClient.getBucket(failedAttemptsKey);
            String currentAttemptsStr = bucket.get();
            return currentAttemptsStr != null ? Integer.parseInt(currentAttemptsStr) : 0;

        } catch (Exception e) {
            LogUtils.error(log, "获取失败次数异常: mobile=" + mobile, e);
            return 0;
        }
    }

    /**
     * 获取剩余可尝试次数
     *
     * @param mobile 手机号
     * @return 剩余次数
     */
    public int getRemainingAttempts(String mobile) {
        int failedAttempts = getFailedAttempts(mobile);
        return Math.max(0, MAX_FAILED_ATTEMPTS - failedAttempts);
    }

    /**
     * 锁定账户
     *
     * @param mobile 手机号
     */
    private void lockAccount(String mobile) {
        try {
            String failedAttemptsKey = FAILED_ATTEMPTS_KEY_PREFIX + mobile;
            String accountLockedKey = ACCOUNT_LOCKED_KEY_PREFIX + mobile;

            // 设置账户锁定标记
            RBucket<String> lockedBucket = redissonClient.getBucket(accountLockedKey);
            lockedBucket.set("locked", LOCK_DURATION);

            // 清除失败次数记录
            redissonClient.getBucket(failedAttemptsKey).delete();

            LogUtils.warn(log, "账户已锁定: mobile={}, lockDuration={}小时", mobile, LOCK_DURATION.toHours());

        } catch (Exception e) {
            LogUtils.error(log, "锁定账户异常: mobile=" + mobile, e);
        }
    }

    /**
     * 获取账户锁定剩余时间（秒）
     *
     * @param mobile 手机号
     * @return 剩余锁定时间（秒），-1表示未锁定
     */
    public long getLockRemainingTime(String mobile) {
        try {
            String accountLockedKey = ACCOUNT_LOCKED_KEY_PREFIX + mobile;
            RBucket<String> bucket = redissonClient.getBucket(accountLockedKey);
            long expireTime = bucket.remainTimeToLive();

            if (expireTime > 0) {
                return expireTime / 1000; // 转换为秒
            }

            return -1;

        } catch (Exception e) {
            LogUtils.error(log, "获取锁定剩余时间异常: mobile=" + mobile, e);
            return -1;
        }
    }
}
