package com.tem.customer.service.partner;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.repository.entity.PartnerNote;
import com.tem.customer.repository.mapper.PartnerNoteMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 企业备注服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Service("partnerNoteService")
public class PartnerNoteServiceImpl extends ServiceImpl<PartnerNoteMapper, PartnerNote> implements PartnerNoteService {

    /**
     * 企业备注最大数量限制
     */
    private static final int MAX_NOTE_COUNT = 8;

    @Override
    public List<PartnerNote> listByPartnerId(Long partnerId) {
        if (partnerId == null) {
            LogUtils.warn(log, "查询企业备注时，企业ID为空");
            return List.of();
        }
        return baseMapper.selectByPartnerIdOrderBySort(partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PartnerNote addPartnerNote(PartnerNote partnerNote) {
        if (partnerNote == null || partnerNote.getPartnerId() == null) {
            LogUtils.warn(log, "添加企业备注时，参数为空");
            return null;
        }

        // 检查数量限制
        if (isExceedLimit(partnerNote.getPartnerId())) {
            LogUtils.warn(log, "企业ID: {} 的备注数量已达到最大限制: {}", partnerNote.getPartnerId(), MAX_NOTE_COUNT);
            return null;
        }

        // 设置排序值
        if (partnerNote.getSortOrder() == null) {
            Integer maxSortOrder = baseMapper.getMaxSortOrderByPartnerId(partnerNote.getPartnerId());
            partnerNote.setSortOrder(maxSortOrder == null ? 1 : maxSortOrder + 1);
        }

        boolean success = save(partnerNote);
        if (success) {
            LogUtils.info(log, "成功添加企业备注，企业ID: {}, 备注ID: {}", partnerNote.getPartnerId(), partnerNote.getId());
            return partnerNote;
        } else {
            LogUtils.error(log, "添加企业备注失败，企业ID: {}", partnerNote.getPartnerId());
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePartnerNote(PartnerNote partnerNote) {
        if (partnerNote == null || partnerNote.getId() == null) {
            LogUtils.warn(log, "更新企业备注时，参数为空");
            return false;
        }
        return updateById(partnerNote);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePartnerNote(Long id) {
        if (id == null) {
            LogUtils.warn(log, "删除企业备注时，ID为空");
            return false;
        }
        return removeById(id);
    }

    @Override
    public boolean isExceedLimit(Long partnerId) {
        if (partnerId == null) {
            return true;
        }
        int count = baseMapper.countByPartnerId(partnerId);
        return count >= MAX_NOTE_COUNT;
    }

    @Override
    public int countByPartnerId(Long partnerId) {
        if (partnerId == null) {
            return 0;
        }
        return baseMapper.countByPartnerId(partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortPosition(Long id, Integer targetPosition) {
        if (id == null || targetPosition == null || targetPosition < 1) {
            LogUtils.warn(log, "调整备注排序位置时，参数无效。ID: {}, 目标位置: {}", id, targetPosition);
            return false;
        }

        // 获取当前备注信息
        PartnerNote currentNote = getById(id);
        if (currentNote == null) {
            LogUtils.warn(log, "调整备注排序位置时，备注不存在。ID: {}", id);
            return false;
        }

        // 获取同一企业下的所有备注，按排序字段排列
        List<PartnerNote> allNotes = listByPartnerId(currentNote.getPartnerId());
        if (allNotes.isEmpty()) {
            LogUtils.warn(log, "调整备注排序位置时，未找到企业备注。企业ID: {}", currentNote.getPartnerId());
            return false;
        }

        // 验证目标位置是否合理
        if (targetPosition > allNotes.size()) {
            LogUtils.warn(log, "调整备注排序位置时，目标位置超出范围。目标位置: {}, 总数: {}", targetPosition, allNotes.size());
            return false;
        }

        // 找到当前备注在列表中的位置
        int currentIndex = -1;
        for (int i = 0; i < allNotes.size(); i++) {
            if (allNotes.get(i).getId().equals(id)) {
                currentIndex = i;
                break;
            }
        }

        if (currentIndex == -1) {
            LogUtils.warn(log, "调整备注排序位置时，在列表中未找到当前备注。ID: {}", id);
            return false;
        }

        // 如果位置没有变化，直接返回成功
        int targetIndex = targetPosition - 1; // 转换为0-based索引
        if (currentIndex == targetIndex) {
            LogUtils.info(log, "备注排序位置无变化，无需调整。ID: {}, 位置: {}", id, targetPosition);
            return true;
        }

        // 重新排列备注列表
        PartnerNote noteToMove = allNotes.remove(currentIndex);
        allNotes.add(targetIndex, noteToMove);

        // 重新分配排序值（从1开始）
        for (int i = 0; i < allNotes.size(); i++) {
            PartnerNote note = allNotes.get(i);
            note.setSortOrder(i + 1);
            if (!updateById(note)) {
                LogUtils.error(log, "更新备注排序值失败。备注ID: {}, 新排序值: {}", note.getId(), i + 1);
                throw new RuntimeException("更新备注排序值失败");
            }
        }

        LogUtils.info(log, "成功调整备注排序位置。备注ID: {}, 从位置 {} 移动到位置 {}", id, currentIndex + 1, targetPosition);
        return true;
    }
}
