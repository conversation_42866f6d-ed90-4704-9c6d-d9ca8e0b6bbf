package com.tem.customer.model.dto.common;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * 代客登录请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@Accessors(chain = true)
public class SpecialLoginDTO {

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long partnerId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 是否H5版本
     */
    private Boolean h5 = false;

    /**
     * 服务URL（可选，用于手工录单）
     */
    private String service;
}
