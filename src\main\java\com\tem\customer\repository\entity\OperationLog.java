package com.tem.customer.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作记录日志实体类
 * 用于记录系统中各种业务操作的日志信息
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
@TableName("t_operation_log")
public class OperationLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * 使用雪花算法生成，通过Mybatis插件自动填充
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务类型（PARTNER_NOTE-企业备注, PARTNER_NOTE_IMAGE-企业备注图片等）
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 业务数据ID
     */
    @TableField("business_id")
    private Long businessId;

    /**
     * 操作类型（CREATE-新增, UPDATE-修改, DELETE-删除, VIEW-查看）
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @TableField("operation_desc")
    private String operationDesc;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 操作人用户名
     */
    @TableField("operator_username")
    private String operatorUsername;

    /**
     * 操作人所属企业ID
     */
    @TableField("partner_id")
    private Long partnerId;

    /**
     * 目标企业ID（被操作的企业备注所属企业）
     */
    @TableField("target_partner_id")
    private Long targetPartnerId;

    /**
     * 操作IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 请求URI
     */
    @TableField("request_uri")
    private String requestUri;

    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 执行耗时（毫秒）
     */
    @TableField("execution_time")
    private Integer executionTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;
}
