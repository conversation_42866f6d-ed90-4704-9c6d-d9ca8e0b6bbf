package com.tem.customer.model.vo;

import lombok.Data;

/**
 * 登录响应VO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class LoginVO {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 企业ID
     */
    private Long partnerId;

    /**
     * 企业名称
     */
    private String partnerName;

    /**
     * 登录时间
     */
    private Long loginTime;

    private String zaituVer;
}
