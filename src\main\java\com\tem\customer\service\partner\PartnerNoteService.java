package com.tem.customer.service.partner;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tem.customer.repository.entity.PartnerNote;

import java.util.List;

/**
 * 企业备注服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface PartnerNoteService extends IService<PartnerNote> {

    /**
     * 根据企业ID查询备注列表
     *
     * @param partnerId 企业ID
     * @return 备注列表，按排序字段升序排列
     */
    List<PartnerNote> listByPartnerId(Long partnerId);

    /**
     * 添加企业备注
     *
     * @param partnerNote 备注信息
     * @return 保存后的备注实体，如果保存失败返回null
     */
    PartnerNote addPartnerNote(PartnerNote partnerNote);

    /**
     * 更新企业备注
     *
     * @param partnerNote 备注信息
     * @return 是否更新成功
     */
    boolean updatePartnerNote(PartnerNote partnerNote);

    /**
     * 删除企业备注
     *
     * @param id 备注ID
     * @return 是否删除成功
     */
    boolean deletePartnerNote(Long id);

    /**
     * 检查企业备注数量是否超过限制
     *
     * @param partnerId 企业ID
     * @return 是否超过限制（最多8条）
     */
    boolean isExceedLimit(Long partnerId);

    /**
     * 统计企业备注数量
     *
     * @param partnerId 企业ID
     * @return 备注数量
     */
    int countByPartnerId(Long partnerId);

    /**
     * 智能调整备注排序位置
     * 将指定备注移动到目标位置，自动调整其他备注的排序值
     *
     * @param id 备注ID
     * @param targetPosition 目标位置（1-based，1表示第一位）
     * @return 是否调整成功
     */
    boolean updateSortPosition(Long id, Integer targetPosition);
}
