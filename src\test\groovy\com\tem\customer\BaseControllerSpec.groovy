package com.tem.customer

import cn.dev33.satoken.stp.StpUtil
import com.iplatform.common.router.context.TrafficTagContext
import com.iplatform.common.router.util.TrafficUtil
import com.tem.customer.shared.utils.UserContextUtil
import org.mockito.Mockito
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

/**
 * Controller测试基类
 * 提供通用的测试功能和静态方法Mock
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class BaseControllerSpec extends Specification {

    def setup() {
        // 设置静态方法Mock
        setupStaticMocks()
    }

    def cleanup() {
        // 清理静态方法Mock
        cleanupStaticMocks()
    }

    /**
     * 设置静态方法Mock
     */
    protected void setupStaticMocks() {
        // Mock Sa-Token相关静态方法
        Mockito.mockStatic(StpUtil.class)
        
        // Mock TrafficUtil相关静态方法
        Mockito.mockStatic(TrafficUtil.class)
        
        // Mock TrafficTagContext相关静态方法
        Mockito.mockStatic(TrafficTagContext.class)
        
        // Mock UserContextUtil相关静态方法
        Mockito.mockStatic(UserContextUtil.class)
    }

    /**
     * 清理静态方法Mock
     */
    protected void cleanupStaticMocks() {
        // 清理所有静态方法Mock
        Mockito.clearAllCaches()
    }

    /**
     * Mock用户登录状态
     */
    protected void mockUserLogin(Long userId, String username) {
        Mockito.when(StpUtil.isLogin()).thenReturn(true)
        Mockito.when(StpUtil.getLoginIdAsLong()).thenReturn(userId)
        Mockito.when(StpUtil.getLoginIdDefaultNull()).thenReturn(userId)
        Mockito.when(StpUtil.getTokenValue()).thenReturn("test-token")
        Mockito.when(StpUtil.getTokenTimeout()).thenReturn(7200)
        Mockito.when(StpUtil.getTokenInfo()).thenReturn([
            tokenName: "satoken",
            tokenValue: "test-token"
        ])
    }

    /**
     * Mock用户未登录状态
     */
    protected void mockUserNotLogin() {
        Mockito.when(StpUtil.isLogin()).thenReturn(false)
        Mockito.when(StpUtil.getLoginIdDefaultNull()).thenReturn(null)
    }

    /**
     * Mock当前用户上下文
     */
    protected void mockCurrentUser(Long userId, Long partnerId, String username) {
        def userDto = new com.tem.platform.api.dto.UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(partnerId)
        userDto.setUsername(username)
        userDto.setMobile("13800138000")
        
        Mockito.when(UserContextUtil.getCurrentUser()).thenReturn(userDto)
    }

    /**
     * Mock路由标签
     */
    protected void mockTrafficTag(String tag) {
        Mockito.when(TrafficTagContext.getTag()).thenReturn(tag)
    }

    /**
     * Mock路由方法
     */
    protected void mockTrafficRoute(Long userId) {
        Mockito.doNothing().when(TrafficUtil.class).routeByUserId(userId)
    }

    /**
     * 重置所有Mock
     */
    protected void resetAllMocks() {
        // 重置Sa-Token Mock
        Mockito.reset(StpUtil.class)
        
        // 重置TrafficUtil Mock
        Mockito.reset(TrafficUtil.class)
        
        // 重置TrafficTagContext Mock
        Mockito.reset(TrafficTagContext.class)
        
        // 重置UserContextUtil Mock
        Mockito.reset(UserContextUtil.class)
    }
}