package com.tem.customer.model.vo.wechat;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企业微信群详情VO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@Accessors(chain = true)
public class WechatGroupDetailVO {

    /**
     * 客户群ID
     */
    private String chatId;

    /**
     * 群名
     */
    private String name;

    /**
     * 群主ID
     */
    private String owner;

    /**
     * 群的创建时间
     */
    private LocalDateTime createTime;

    /**
     * 群公告
     */
    private String notice;

    /**
     * 群成员数量
     */
    private Integer memberCount;

    /**
     * 群管理员数量
     */
    private Integer adminCount;

    /**
     * 群成员列表
     */
    private List<MemberVO> memberList;

    /**
     * 群管理员列表
     */
    private List<AdminVO> adminList;

    /**
     * 当前群成员版本号
     */
    private String memberVersion;

    /**
     * 群成员信息VO
     */
    @Data
    @Accessors(chain = true)
    public static class MemberVO {

        /**
         * 群成员id
         */
        private String userId;

        /**
         * 成员类型。1 - 企业成员；2 - 外部联系人
         */
        private Integer type;

        /**
         * 成员类型描述
         */
        private String typeDesc;

        /**
         * 外部联系人在微信开放平台的唯一身份标识
         */
        private String unionId;

        /**
         * 入群时间
         */
        private LocalDateTime joinTime;

        /**
         * 入群方式
         * 1 - 由群成员邀请入群（直接邀请入群）
         * 2 - 由群成员邀请入群（通过邀请链接入群）
         * 3 - 通过扫描群二维码入群
         */
        private Integer joinScene;

        /**
         * 入群方式描述
         */
        private String joinSceneDesc;

        /**
         * 邀请者ID
         */
        private String invitorUserId;

        /**
         * 在群里的昵称
         */
        private String groupNickname;

        /**
         * 名字
         */
        private String name;
    }

    /**
     * 群管理员信息VO
     */
    @Data
    @Accessors(chain = true)
    public static class AdminVO {

        /**
         * 群管理员userid
         */
        private String userId;
    }
}
