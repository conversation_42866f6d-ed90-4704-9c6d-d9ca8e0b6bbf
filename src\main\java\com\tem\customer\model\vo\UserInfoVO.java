package com.tem.customer.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 用户信息响应VO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserInfoVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;


    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 企业ID
     */
    private Long partnerId;

    /**
     * 企业名称
     */
    private String partnerName;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 用户权限列表
     */
    private List<String> permissions;

    /**
     * 是否为超级管理员
     */
    private Boolean isSuperAdmin;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 最后活跃时间
     */
    private Long lastActiveTime;
}
