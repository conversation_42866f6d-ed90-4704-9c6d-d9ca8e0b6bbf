package com.tem.customer.shared.common;

/**
 * 系统常量类
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public class Constant {

    /**
     * TraceId常量
     */
    public static final String TRACE_ID = "traceId";

    public static final String[] WHITE_LIST = {
            "/api/cx/customer/auth/**",
    };

    /**
     * ========== 登录相关常量 ==========
     */
    public static class Login {
        /**
         * 登录时间会话属性键
         */
        public static final String LOGIN_TIME = "loginTime";

        /**
         * 最大登录间隔小时数配置键
         */
        public static final String LOGIN_MOST_INTERVAL_HOUR = "login.most.interval.hour";

    }


    /**
     * ========== 企业用户相关常量 ==========
     */
    public static class PartnerUser {

        /**
         * 关系等级常量
         */
        public static final class RelationshipGrade {
            /** 企业级别 */
            public static final int PARTNER_LEVEL = 30;
            /** 渠道级别 */
            public static final int CHANNEL_LEVEL = 20;
        }

        /**
         * 权限相关常量
         */
        public static final class Permission {
            /** 有权限标识 */
            public static final int HAS_PERMISSION = 0;
            /** 服务订单权限资源 */
            public static final String SERVICE_ORDER_RESOURCE = "service_order";
            /** 代客下单权限操作 */
            public static final String CUSTOMER_PROXY_OPERATION = "C";
            /** 手工录单权限操作 */
            public static final String MANUAL_ORDER_OPERATION = "H";
        }

        /**
         * 代客登录相关常量
         */
        public static final class SpecialLogin {
            /** 默认H5域名 */
            public static final String DEFAULT_H5_DOMAIN = "https://m.z-trip.cn";
            /** H5登录路径 */
            public static final String H5_LOGIN_PATH = "/index";
            /** 用户ID参数名 */
            public static final String USER_ID_PARAM = "userId";
            /** 客服ID数据键 */
            public static final String SERVICE_ID_DATA_KEY = "data_serviceId";

        }

        /**
         * 参数和会话键常量
         */
        public static final class Parameters {
            /** 企业ID参数键 */
            public static final String PARAM_KEY_PARTNER = "partnerId";
            /** 企业ID会话键 */
            public static final String SESSION_KEY_PARTNER = "partnerId";
        }



    }

}
