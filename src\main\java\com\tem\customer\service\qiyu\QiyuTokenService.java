package com.tem.customer.service.qiyu;

import com.tem.customer.model.dto.qiyu.QiyuTokenResponse;

/**
 * 七鱼Token管理服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface QiyuTokenService {

    /**
     * 获取Token
     *
     * @param requestAppId     请求的AppId
     * @param requestAppSecret 请求的AppSecret
     * @return Token响应
     */
    QiyuTokenResponse getToken(String requestAppId, String requestAppSecret);

    /**
     * 验证Token是否有效
     *
     * @param token Token值
     * @return 验证结果
     */
    boolean validateToken(String token);
}
