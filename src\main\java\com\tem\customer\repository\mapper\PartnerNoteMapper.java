package com.tem.customer.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tem.customer.repository.entity.PartnerNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业备注Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Mapper
public interface PartnerNoteMapper extends BaseMapper<PartnerNote> {

    /**
     * 根据企业ID查询备注列表，按排序字段升序排列
     *
     * @param partnerId 企业ID
     * @return 备注列表
     */
    List<PartnerNote> selectByPartnerIdOrderBySort(@Param("partnerId") Long partnerId);

    /**
     * 统计企业的备注数量
     *
     * @param partnerId 企业ID
     * @return 备注数量
     */
    int countByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 获取企业备注的最大排序值
     *
     * @param partnerId 企业ID
     * @return 最大排序值
     */
    Integer getMaxSortOrderByPartnerId(@Param("partnerId") Long partnerId);
}
