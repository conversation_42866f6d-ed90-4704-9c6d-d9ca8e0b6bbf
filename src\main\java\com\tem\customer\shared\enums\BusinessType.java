package com.tem.customer.shared.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 * 用于标识操作日志的业务类型
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@AllArgsConstructor
public enum BusinessType {

    /**
     * 企业备注
     */
    PARTNER_NOTE("PARTNER_NOTE", "企业备注"),

    /**
     * 企业备注图片
     */
    PARTNER_NOTE_IMAGE("PARTNER_NOTE_IMAGE", "企业备注图片"),

    /**
     * 企业微信群绑定关系
     */
    PARTNER_WECHAT_GROUP("PARTNER_WECHAT_GROUP", "企业微信群绑定关系"),

    /**
     * 微信用户绑定关系
     */
    WECHAT_USER_BINDING("WECHAT_USER_BINDING", "微信用户绑定关系"),

    /**
     * 企业信息
     */
    PARTNER("PARTNER", "企业信息"),

    /**
     * 企业用户
     */
    PARTNER_USER("PARTNER_USER", "企业用户"),

    /**
     * 用户管理
     */
    USER("USER", "用户管理"),

    /**
     * 系统配置
     */
    SYSTEM_CONFIG("SYSTEM_CONFIG", "系统配置"),

    /**
     * 其他
     */
    OTHER("OTHER", "其他");

    /**
     * 业务类型代码
     */
    private final String code;

    /**
     * 业务类型描述
     */
    private final String description;

    /**
     * 根据代码获取业务类型
     *
     * @param code 业务类型代码
     * @return 业务类型枚举，如果未找到则返回OTHER
     */
    public static BusinessType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return OTHER;
        }
        
        for (BusinessType type : BusinessType.values()) {
            if (type.getCode().equals(code.trim())) {
                return type;
            }
        }
        return OTHER;
    }

    /**
     * 检查是否为有效的业务类型代码
     *
     * @param code 业务类型代码
     * @return 如果是有效代码返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        for (BusinessType type : BusinessType.values()) {
            if (type.getCode().equals(code.trim())) {
                return true;
            }
        }
        return false;
    }
}
