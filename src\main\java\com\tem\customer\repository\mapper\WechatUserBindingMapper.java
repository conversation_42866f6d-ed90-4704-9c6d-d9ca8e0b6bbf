package com.tem.customer.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tem.customer.repository.entity.WechatUserBinding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微信用户绑定关系Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface WechatUserBindingMapper extends BaseMapper<WechatUserBinding> {

    /**
     * 根据企业ID查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> selectByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据企业ID和用户ID查询绑定关系
     *
     * @param partnerId 企业ID
     * @param userId 用户ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> selectByPartnerIdAndUserId(@Param("partnerId") Long partnerId, @Param("userId") Long userId);


    /**
     * 根据UnionID查询绑定关系
     *
     * @param unionId 微信UnionID
     * @return 绑定关系
     */
    WechatUserBinding selectByUnionId(@Param("unionId") String unionId);


    /**
     * 根据来源类型查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @param sourceType 来源类型
     * @return 绑定关系列表
     */
    List<WechatUserBinding> selectBySourceType(@Param("partnerId") Long partnerId, @Param("sourceType") String sourceType);


    /**
     * 检查UnionID是否已存在（排除指定ID）
     *
     * @param partnerId 企业ID
     * @param unionId 微信UnionID
     * @param excludeId 排除的记录ID
     * @return 是否存在
     */
    boolean existsByUnionIdExcludeId(@Param("partnerId") Long partnerId, @Param("unionId") String unionId, @Param("excludeId") Long excludeId);


    /**
     * 根据企业ID统计绑定关系数量
     *
     * @param partnerId 企业ID
     * @return 绑定关系数量
     */
    int countByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据企业ID统计有效绑定关系数量
     *
     * @param partnerId 企业ID
     * @return 有效绑定关系数量
     */
    int countValidByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据微信群ID查询绑定关系列表
     *
     * @param chatId 微信群ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> selectByChatId(@Param("chatId") String chatId);

    /**
     * 根据企业ID和微信群ID查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @param chatId 微信群ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> selectByPartnerIdAndChatId(@Param("partnerId") Long partnerId, @Param("chatId") String chatId);

    /**
     * 根据企业ID和用户ID查询用户所在的微信群绑定关系
     *
     * @param partnerId 企业ID
     * @param userId 用户ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> selectGroupsByUserId(@Param("partnerId") Long partnerId, @Param("userId") Long userId);

    /**
     * 根据微信群ID和用户ID查询绑定关系
     *
     * @param chatId 微信群ID
     * @param userId 用户ID
     * @return 绑定关系
     */
    WechatUserBinding selectByChatIdAndUserId(@Param("chatId") String chatId, @Param("userId") Long userId);

    /**
     * 根据微信群ID统计绑定关系数量
     *
     * @param chatId 微信群ID
     * @return 绑定关系数量
     */
    int countByChatId(@Param("chatId") String chatId);

    /**
     * 根据微信群ID统计有效绑定关系数量
     *
     * @param chatId 微信群ID
     * @return 有效绑定关系数量
     */
    int countValidByChatId(@Param("chatId") String chatId);

    /**
     * 检查微信群和用户绑定关系是否已存在（排除指定ID）
     *
     * @param chatId 微信群ID
     * @param userId 用户ID
     * @param excludeId 排除的记录ID
     * @return 是否存在
     */
    boolean existsByChatIdAndUserIdExcludeId(@Param("chatId") String chatId, @Param("userId") Long userId, @Param("excludeId") Long excludeId);


    /**
     * 根据UnionID全局查询绑定关系（不限制企业ID）
     *
     * @param unionId 微信UnionID
     * @return 绑定关系
     */
    WechatUserBinding selectByUnionIdGlobal(@Param("unionId") String unionId);
}
