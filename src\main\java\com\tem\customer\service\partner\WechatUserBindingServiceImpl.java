package com.tem.customer.service.partner;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.convert.WechatUserBindingConverter;
import com.tem.customer.model.vo.partner.WechatUserBindingVO;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.repository.mapper.WechatUserBindingMapper;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.platform.api.PartnerService;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.PartnerDto;
import com.tem.platform.api.dto.UserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 微信用户绑定关系服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Service("wechatUserBindingService")
@RequiredArgsConstructor
public class WechatUserBindingServiceImpl extends ServiceImpl<WechatUserBindingMapper, WechatUserBinding> implements WechatUserBindingService {

    private final PartnerWechatGroupService partnerWechatGroupService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;


    @Override
    public List<WechatUserBinding> listByPartnerId(Long partnerId) {
        if (partnerId == null) {
            LogUtils.warn(log, "查询微信用户绑定关系时，企业ID为空");
            return List.of();
        }
        return baseMapper.selectByPartnerId(partnerId);
    }

    @Override
    public List<WechatUserBinding> listByPartnerIdAndUserId(Long partnerId, Long userId) {
        if (partnerId == null || userId == null) {
            LogUtils.warn(log, "查询微信用户绑定关系时，企业ID或用户ID为空，partnerId: {}, userId: {}", partnerId, userId);
            return List.of();
        }
        return baseMapper.selectByPartnerIdAndUserId(partnerId, userId);
    }

    @Override
    public WechatUserBinding getByUnionId(String unionId) {
        if (!StringUtils.hasText(unionId)) {
            LogUtils.warn(log, "根据UnionID查询绑定关系时，参数为空");
            return null;
        }
        return baseMapper.selectByUnionId(unionId);
    }

    @Override
    public List<WechatUserBinding> listBySourceType(Long partnerId, String sourceType) {
        if (partnerId == null || !StringUtils.hasText(sourceType)) {
            LogUtils.warn(log, "根据来源类型查询绑定关系时，参数为空，partnerId: {}, sourceType: {}", partnerId, sourceType);
            return List.of();
        }
        return baseMapper.selectBySourceType(partnerId, sourceType);
    }

    @Override
    public int countByPartnerId(Long partnerId) {
        if (partnerId == null) {
            return 0;
        }
        return baseMapper.countByPartnerId(partnerId);
    }

    @Override
    public int countValidByPartnerId(Long partnerId) {
        if (partnerId == null) {
            return 0;
        }
        return baseMapper.countValidByPartnerId(partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createWechatUserBinding(WechatUserBinding wechatUserBinding) {
        // 参数校验
        validateWechatUserBinding(wechatUserBinding, false);

        // 检查唯一性约束  单聊用户检查
        validateUniqueness(wechatUserBinding, null);

        // chatId用户绑定关系检查：一个chatId可以绑定多个用户，但是要校验当前chatId是否已经绑定，一个userId可以绑定多个chatId
        validateChatIdBinding(wechatUserBinding, null);

        // 设置默认值
        setDefaultValues(wechatUserBinding);

        boolean result = save(wechatUserBinding);
        if (result) {
            LogUtils.info(log, "创建微信用户绑定关系成功，企业ID：{}，用户ID：{}，来源类型：{}",
                    wechatUserBinding.getPartnerId(), wechatUserBinding.getUserId(), wechatUserBinding.getSourceType());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWechatUserBinding(WechatUserBinding wechatUserBinding) {
        // 参数校验
        validateWechatUserBinding(wechatUserBinding, true);

        // 检查记录是否存在
        WechatUserBinding existing = getById(wechatUserBinding.getId());
        if (existing == null) {
            throw BusinessException.error("微信用户绑定关系不存在");
        }

        // 检查唯一性约束
        validateUniqueness(wechatUserBinding, wechatUserBinding.getId());

        // chatId用户绑定关系检查
        validateChatIdBinding(wechatUserBinding, wechatUserBinding.getId());

        boolean result = updateById(wechatUserBinding);
        if (result) {
            LogUtils.info(log, "更新微信用户绑定关系成功，记录ID：{}，用户ID：{}",
                    wechatUserBinding.getId(), wechatUserBinding.getUserId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWechatUserBinding(Long id) {
        if (id == null) {
            throw BusinessException.error("记录ID不能为空");
        }

        WechatUserBinding existing = getById(id);
        if (existing == null) {
            throw BusinessException.error("微信用户绑定关系不存在");
        }

        boolean result = removeById(id);
        if (result) {
            LogUtils.info(log, "删除微信用户绑定关系成功，记录ID：{}，用户ID：{}",
                    id, existing.getUserId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        if (id == null) {
            throw BusinessException.error("记录ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw BusinessException.error("状态值必须为0或1");
        }

        WechatUserBinding existing = getById(id);
        if (existing == null) {
            throw BusinessException.error("微信用户绑定关系不存在");
        }

        existing.setStatus(status);
        boolean result = updateById(existing);
        if (result) {
            LogUtils.info(log, "更新微信用户绑定关系状态成功，记录ID：{}，状态：{}", id, status);
        }
        return result;
    }


    /**
     * 校验微信用户绑定关系参数
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @param isUpdate          是否为更新操作
     */
    private void validateWechatUserBinding(WechatUserBinding wechatUserBinding, boolean isUpdate) {
        if (wechatUserBinding == null) {
            throw BusinessException.error("微信用户绑定关系不能为空");
        }
        if (isUpdate && wechatUserBinding.getId() == null) {
            throw BusinessException.error("更新时记录ID不能为空");
        }
        if (wechatUserBinding.getPartnerId() == null) {
            throw BusinessException.error("企业ID不能为空");
        }
        if (!StringUtils.hasText(wechatUserBinding.getSourceType())) {
            throw BusinessException.error("来源类型不能为空");
        }

        // 至少需要有一个微信标识
        if (!StringUtils.hasText(wechatUserBinding.getUnionId()) && !StringUtils.hasText(wechatUserBinding.getChatId())) {
            throw BusinessException.error("至少需要提供一个微信标识（企业微信外部联系人ID、UnionID或OpenID）");
        }

        // 校验来源类型
        boolean validSourceType = false;
        for (WechatUserBinding.SourceType type : WechatUserBinding.SourceType.values()) {
            if (type.getCode().equals(wechatUserBinding.getSourceType())) {
                validSourceType = true;
                break;
            }
        }
        if (!validSourceType) {
            throw BusinessException.error("无效的来源类型：" + wechatUserBinding.getSourceType());
        }
    }

    /**
     * 检查唯一性约束
     * 单聊绑定关系
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @param excludeId         排除的记录ID
     */
    private void validateUniqueness(WechatUserBinding wechatUserBinding, Long excludeId) {
        Long partnerId = wechatUserBinding.getPartnerId();
        // 检查UnionID唯一性
        if (StringUtils.hasText(wechatUserBinding.getUnionId())) {
            if (baseMapper.existsByUnionIdExcludeId(partnerId, wechatUserBinding.getUnionId(), excludeId)) {
                throw BusinessException.error("微信UnionID已存在，不能重复绑定");
            }
        }
    }

    /**
     * 检查chatId用户绑定关系
     * 一个chatId可以绑定多个用户，但是要校验当前chatId是否已经绑定，一个userId可以绑定多个chatId
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @param excludeId         排除的记录ID
     */
    private void validateChatIdBinding(WechatUserBinding wechatUserBinding, Long excludeId) {
        // 如果包含chatId，进行群聊相关校验
        if (StringUtils.hasText(wechatUserBinding.getChatId())) {
            // 验证微信群是否存在且属于指定企业
            validateWechatGroup(wechatUserBinding.getPartnerId(), wechatUserBinding.getChatId());

            // 如果同时提供了userId，检查该用户是否已经绑定到该群
            if (wechatUserBinding.getUserId() != null) {
                if (existsByChatIdAndUserId(wechatUserBinding.getChatId(), wechatUserBinding.getUserId(), excludeId)) {
                    throw BusinessException.error("该用户已绑定到此微信群，不能重复绑定");
                }
            }
        }
    }

    /**
     * 设置默认值
     *
     * @param wechatUserBinding 微信用户绑定关系
     */
    private void setDefaultValues(WechatUserBinding wechatUserBinding) {
        if (wechatUserBinding.getStatus() == null) {
            wechatUserBinding.setStatus(WechatUserBinding.Status.VALID.getCode());
        }
    }


    @Override
    public List<WechatUserBinding> listByChatId(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            LogUtils.warn(log, "查询微信群绑定关系时，群ID为空");
            return List.of();
        }
        return baseMapper.selectByChatId(chatId);
    }

    @Override
    public List<WechatUserBinding> listByPartnerIdAndChatId(Long partnerId, String chatId) {
        if (partnerId == null || !StringUtils.hasText(chatId)) {
            LogUtils.warn(log, "查询企业微信群绑定关系时，参数为空，partnerId: {}, chatId: {}", partnerId, chatId);
            return List.of();
        }
        return baseMapper.selectByPartnerIdAndChatId(partnerId, chatId);
    }

    @Override
    public List<WechatUserBinding> listGroupsByUserId(Long partnerId, Long userId) {
        if (partnerId == null || userId == null) {
            LogUtils.warn(log, "查询用户所在微信群时，参数为空，partnerId: {}, userId: {}", partnerId, userId);
            return List.of();
        }
        return baseMapper.selectGroupsByUserId(partnerId, userId);
    }

    @Override
    public WechatUserBinding getByChatIdAndUserId(String chatId, Long userId) {
        if (!StringUtils.hasText(chatId) || userId == null) {
            LogUtils.warn(log, "根据群ID和用户ID查询绑定关系时，参数为空，chatId: {}, userId: {}", chatId, userId);
            return null;
        }
        return baseMapper.selectByChatIdAndUserId(chatId, userId);
    }

    @Override
    public int countByChatId(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            return 0;
        }
        return baseMapper.countByChatId(chatId);
    }

    @Override
    public int countValidByChatId(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            return 0;
        }
        return baseMapper.countValidByChatId(chatId);
    }

    @Override
    public boolean existsByChatIdAndUserId(String chatId, Long userId, Long excludeId) {
        if (!StringUtils.hasText(chatId) || userId == null) {
            return false;
        }
        return baseMapper.existsByChatIdAndUserIdExcludeId(chatId, userId, excludeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createGroupStaffBinding(WechatUserBinding wechatUserBinding) {
        // 参数校验
        validateGroupStaffBinding(wechatUserBinding, false);

        // 验证微信群是否存在且属于指定企业
        validateWechatGroup(wechatUserBinding.getPartnerId(), wechatUserBinding.getChatId());

        // 检查群内人员绑定唯一性约束
        validateGroupStaffUniqueness(wechatUserBinding, null);

        // 设置群内人员绑定的默认值
        setGroupStaffDefaultValues(wechatUserBinding);

        boolean result = save(wechatUserBinding);
        if (result) {
            LogUtils.info(log, "创建群内人员绑定关系成功，企业ID：{}，微信群ID：{}，用户ID：{}",
                    wechatUserBinding.getPartnerId(), wechatUserBinding.getChatId(), wechatUserBinding.getUserId());
        }
        return result;
    }

    /**
     * 校验群内人员绑定关系参数
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @param isUpdate          是否为更新操作
     */
    private void validateGroupStaffBinding(WechatUserBinding wechatUserBinding, boolean isUpdate) {
        if (wechatUserBinding == null) {
            throw BusinessException.error("群内人员绑定关系不能为空");
        }
        if (isUpdate && wechatUserBinding.getId() == null) {
            throw BusinessException.error("更新时记录ID不能为空");
        }
        if (wechatUserBinding.getPartnerId() == null) {
            throw BusinessException.error("企业ID不能为空");
        }
        if (!StringUtils.hasText(wechatUserBinding.getChatId())) {
            throw BusinessException.error("微信群ID不能为空");
        }
        if (wechatUserBinding.getUserId() == null) {
            throw BusinessException.error("用户ID不能为空");
        }
        if (!StringUtils.hasText(wechatUserBinding.getSourceType())) {
            throw BusinessException.error("来源类型不能为空");
        }

        // 群内人员绑定必须是企业微信来源
        if (!WechatUserBinding.SourceType.WORK_WECHAT.getCode().equals(wechatUserBinding.getSourceType())) {
            throw BusinessException.error("群内人员绑定的来源类型必须为WORK_WECHAT");
        }
    }

    /**
     * 验证微信群是否存在且属于指定企业
     *
     * @param partnerId 企业ID
     * @param chatId    微信群ID
     */
    private void validateWechatGroup(Long partnerId, String chatId) {
        PartnerWechatGroup group = partnerWechatGroupService.getByChatId(chatId);
        if (group == null) {
            throw BusinessException.error("微信群不存在或未绑定企业");
        }
        if (!partnerId.equals(group.getPartnerId())) {
            throw BusinessException.error("企业ID与微信群所属企业不匹配");
        }
        if (!PartnerWechatGroup.Status.ENABLED.getCode().equals(group.getStatus())) {
            throw BusinessException.error("微信群已禁用，无法创建绑定关系");
        }
    }

    /**
     * 检查群内人员绑定唯一性约束
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @param excludeId         排除的记录ID
     */
    private void validateGroupStaffUniqueness(WechatUserBinding wechatUserBinding, Long excludeId) {
        // 检查同一企业的同一用户是否已绑定到同一微信群
        if (existsByChatIdAndUserId(wechatUserBinding.getChatId(), wechatUserBinding.getUserId(), excludeId)) {
            throw BusinessException.error("该人员已绑定到此微信群");
        }
    }

    /**
     * 设置群内人员绑定的默认值
     *
     * @param wechatUserBinding 微信用户绑定关系
     */
    private void setGroupStaffDefaultValues(WechatUserBinding wechatUserBinding) {
        if (wechatUserBinding.getStatus() == null) {
            wechatUserBinding.setStatus(WechatUserBinding.Status.VALID.getCode());
        }
        // 群内人员绑定的来源类型固定为群内人员
        wechatUserBinding.setSourceType(WechatUserBinding.SourceType.GROUP_STAFF.getCode());
    }

    @Override
    public List<WechatUserBindingVO> listByChatIdWithGroupName(String chatId) {
        List<WechatUserBinding> bindings = listByChatId(chatId);
        if (CollectionUtils.isEmpty(bindings)) {
            LogUtils.info(log, "微信群绑定列表为空, chatId:{}", chatId);
            return Lists.newArrayList();
        }
        return fillUserInfo(bindings);
    }

    @Override
    public List<WechatUserBindingVO> listByPartnerIdAndChatIdWithGroupName(Long partnerId, String chatId) {
        List<WechatUserBinding> bindings = listByPartnerIdAndChatId(partnerId, chatId);
        if(CollectionUtils.isEmpty(bindings)){
            LogUtils.info(log, "微信群绑定列表为空, partnerId:{}, chatId:{}", partnerId, chatId);
            return Lists.newArrayList();
        }
        return fillUserInfo(bindings);
    }

    @Override
    public List<WechatUserBindingVO> listGroupsByUserIdWithGroupName(Long partnerId, Long userId) {
        List<WechatUserBinding> bindings = listGroupsByUserId(partnerId, userId);
        if (CollectionUtils.isEmpty(bindings)) {
            LogUtils.info(log, "用户所在微信群列表为空, partnerId:{}, userId:{}", partnerId, userId);
            return Lists.newArrayList();
        }
        return fillUserInfo(bindings);
    }

    /**
     * 填充用户信息和企业信息到VO列表
     *
     * @param bindings 绑定关系列表
     * @return 填充用户信息和企业信息后的VO列表
     */
    private List<WechatUserBindingVO> fillUserInfo(List<WechatUserBinding> bindings) {
        if (CollectionUtils.isEmpty(bindings)) {
            return Lists.newArrayList();
        }

        // 提取用户ID列表
        List<Long> userIdList = bindings.stream()
                .map(WechatUserBinding::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 查询用户信息
        Map<Long, UserDto> userMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userIdList)) {
            try {
                List<UserDto> userDtoList = userService.getUsers(userIdList).getData();
                userMap = userDtoList.stream()
                        .collect(Collectors.toMap(UserDto::getId, user -> user));
            } catch (Exception e) {
                LogUtils.error(log, "查询用户信息失败", e);
            }
        }

        // 提取企业ID列表
        List<Long> partnerIdList = bindings.stream()
                .map(WechatUserBinding::getPartnerId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 查询企业信息
        Map<Long, PartnerDto> partnerMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(partnerIdList)) {
            try {
                ResponseDto<List<PartnerDto>> partnerResponse = partnerService.findByBpIds(partnerIdList);
                if (partnerResponse != null && partnerResponse.isSuccess() && partnerResponse.getData() != null) {
                    partnerMap = partnerResponse.getData().stream()
                            .collect(Collectors.toMap(PartnerDto::getId, partner -> partner));
                    LogUtils.info(log, "批量查询企业信息成功，企业数量: {}", partnerMap.size());
                } else {
                    LogUtils.warn(log, "批量查询企业信息失败，响应: {}", partnerResponse);
                }
            } catch (Exception e) {
                LogUtils.error(log, "批量查询企业信息失败", e);
            }
        }

        // 转换为VO并填充用户信息和企业信息
        List<WechatUserBindingVO> voList = WechatUserBindingConverter.INSTANCE.toVOList(bindings);
        Map<Long, UserDto> finalUserMap = userMap;
        Map<Long, PartnerDto> finalPartnerMap = partnerMap;
        voList.forEach(vo -> {
            // 填充用户信息
            UserDto userDto = finalUserMap.get(vo.getUserId());
            if (userDto != null) {
                vo.setUserName(userDto.getFullname());
                vo.setMobile(userDto.getMobile());
                vo.setEmail(userDto.getEmail());
            }

            // 填充企业信息
            PartnerDto partnerDto = finalPartnerMap.get(vo.getPartnerId());
            if (partnerDto != null) {
                vo.setPartnerName(partnerDto.getName());
            }
        });

        return voList;
    }

    /**
     * 填充单个绑定关系的用户信息和企业信息
     *
     * @param binding 绑定关系实体
     * @return 填充信息后的VO对象
     */
    private WechatUserBindingVO fillSingleUserInfo(WechatUserBinding binding) {
        if (binding == null) {
            return null;
        }

        // 转换为VO对象
        WechatUserBindingVO vo = WechatUserBindingConverter.INSTANCE.toVO(binding);

        // 查询并填充用户信息
        if (binding.getUserId() != null) {
            try {
                ResponseDto<UserDto> userResponse = userService.getUser(binding.getUserId());
                if (userResponse != null && userResponse.isSuccess() && userResponse.getData() != null) {
                    UserDto userDto = userResponse.getData();
                    vo.setUserName(userDto.getFullname());
                    vo.setMobile(userDto.getMobile());
                    vo.setEmail(userDto.getEmail());
                    LogUtils.debug(log, "填充用户信息成功，用户ID: {}, 用户名: {}", binding.getUserId(), userDto.getFullname());
                } else {
                    LogUtils.warn(log, "查询用户信息失败，用户ID: {}, 响应: {}", binding.getUserId(), userResponse);
                }
            } catch (Exception e) {
                LogUtils.error(log, "查询用户信息异常，用户ID: {}", binding.getUserId(), e);
            }
        }

        // 查询并填充企业信息
        if (binding.getPartnerId() != null) {
            try {
                ResponseDto<PartnerDto> partnerResponse = partnerService.findById(binding.getPartnerId());
                if (partnerResponse != null && partnerResponse.isSuccess() && partnerResponse.getData() != null) {
                    PartnerDto partnerDto = partnerResponse.getData();
                    vo.setPartnerName(partnerDto.getName());
                    LogUtils.debug(log, "填充企业信息成功，企业ID: {}, 企业名称: {}", binding.getPartnerId(), partnerDto.getName());
                } else {
                    LogUtils.warn(log, "查询企业信息失败，企业ID: {}, 响应: {}", binding.getPartnerId(), partnerResponse);
                }
            } catch (Exception e) {
                LogUtils.error(log, "查询企业信息异常，企业ID: {}", binding.getPartnerId(), e);
            }
        }

        return vo;
    }


    @Override
    public WechatUserBinding getByUnionIdGlobal(String unionId) {
        if (!StringUtils.hasText(unionId)) {
            LogUtils.warn(log, "全局根据UnionID查询绑定关系时，UnionID为空");
            return null;
        }
        return baseMapper.selectByUnionIdGlobal(unionId);
    }

    @Override
    public WechatUserBindingVO getByUnionIdWithFullInfo(String unionId) {
        WechatUserBinding binding = getByUnionId(unionId);
        return fillSingleUserInfo(binding);
    }

    @Override
    public WechatUserBindingVO getByChatIdAndUserIdWithFullInfo(String chatId, Long userId) {
        WechatUserBinding binding = getByChatIdAndUserId(chatId, userId);
        return fillSingleUserInfo(binding);
    }

    @Override
    public List<WechatUserBindingVO> fillUserInfoList(List<WechatUserBinding> bindings) {
        return fillUserInfo(bindings);
    }
}
