package com.tem.customer.controller.partner;

import com.tem.customer.model.convert.WechatUserBindingConverter;
import com.tem.customer.model.dto.partner.WechatUserBindingDTO;
import com.tem.customer.model.vo.partner.WechatUserBindingVO;
import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.service.partner.WechatUserBindingService;
import com.tem.customer.shared.annotation.OperationLog;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;
import com.tem.platform.api.UserService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 微信用户绑定关系控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/wechat-user-bindings")
@RequiredArgsConstructor
@Validated
public class PartnerWechatUserBindingController {

    private final WechatUserBindingService wechatUserBindingService;


    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    /**
     * 根据企业ID查询绑定关系列表（包含完整用户信息和企业信息）
     *
     * @param partnerId 企业ID
     * @return 绑定关系列表
     */
    @GetMapping("/partner/{partnerId}")
    public Result<List<WechatUserBindingVO>> listByPartnerId(@PathVariable @NotNull Long partnerId) {
        List<WechatUserBinding> bindings = wechatUserBindingService.listByPartnerId(partnerId);
        // 使用 fillUserInfoList 方法填充完整信息
        List<WechatUserBindingVO> voList = wechatUserBindingService.fillUserInfoList(bindings);
        return Result.success(voList);
    }

    /**
     * 根据企业ID和用户ID查询绑定关系（包含完整用户信息和企业信息）
     *
     * @param partnerId 企业ID
     * @param userId 用户ID
     * @return 绑定关系列表
     */
    @GetMapping("/partner/{partnerId}/user/{userId}")
    public Result<List<WechatUserBindingVO>> listByPartnerIdAndUserId(@PathVariable @NotNull Long partnerId,
                                                                      @PathVariable @NotNull Long userId) {
        List<WechatUserBinding> bindings = wechatUserBindingService.listByPartnerIdAndUserId(partnerId, userId);
        // 使用 fillUserInfoList 方法填充完整信息
        List<WechatUserBindingVO> voList = wechatUserBindingService.fillUserInfoList(bindings);
        return Result.success(voList);
    }

    /**
     * 根据来源类型查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @param sourceType 来源类型
     * @return 绑定关系列表
     */
    @GetMapping("/partner/{partnerId}/source/{sourceType}")
    public Result<List<WechatUserBindingVO>> listBySourceType(@PathVariable @NotNull Long partnerId, 
                                                              @PathVariable @NotNull String sourceType) {
        List<WechatUserBinding> bindings = wechatUserBindingService.listBySourceType(partnerId, sourceType);
        List<WechatUserBindingVO> voList = WechatUserBindingConverter.INSTANCE.toVOList(bindings);
        return Result.success(voList);
    }

    /**
     * 根据UnionID查询绑定关系详情（包含完整用户信息和企业信息）
     *
     * @param unionId UnionID
     * @return 绑定关系详情
     */
    @GetMapping("/{unionId}")
    public Result<WechatUserBindingVO> getByUnionId(@PathVariable @NotNull String unionId) {
        WechatUserBindingVO vo = wechatUserBindingService.getByUnionIdWithFullInfo(unionId);
        if (vo == null) {
            return Result.error("微信用户绑定关系不存在");
        }
        return Result.success(vo);
    }



    /**
     * 创建微信用户绑定关系
     *
     * @param dto 微信用户绑定关系DTO
     * @return 创建结果
     */
    @PostMapping
    @OperationLog(businessType = BusinessType.WECHAT_USER_BINDING, operationType = OperationType.CREATE,
            description = "创建微信用户绑定关系",
            businessIdExpression = "#dto.userId",
            targetPartnerIdExpression = "#dto.partnerId")
    public Result<Void> create(@RequestBody @Valid WechatUserBindingDTO dto) {
        WechatUserBinding binding = WechatUserBindingConverter.INSTANCE.toEntity(dto);
        boolean success = wechatUserBindingService.createWechatUserBinding(binding);
        return success ? Result.success() : Result.error("创建失败");
    }

    /**
     * 更新微信用户绑定关系
     *
     * @param id  记录ID
     * @param dto 微信用户绑定关系DTO
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @OperationLog(businessType = BusinessType.WECHAT_USER_BINDING, operationType = OperationType.UPDATE,
            description = "更新微信用户绑定关系",
            businessIdExpression = "#id",
            targetPartnerIdExpression = "#dto.partnerId")
    public Result<Void> update(@PathVariable @NotNull Long id, @RequestBody @Valid WechatUserBindingDTO dto) {
        dto.setId(id);
        WechatUserBinding binding = WechatUserBindingConverter.INSTANCE.toEntity(dto);
        boolean success = wechatUserBindingService.updateWechatUserBinding(binding);
        return success ? Result.success() : Result.error("更新失败");
    }

    /**
     * 删除微信用户绑定关系
     *
     * @param id 记录ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @OperationLog(businessType = BusinessType.WECHAT_USER_BINDING, operationType = OperationType.DELETE,
            description = "删除微信用户绑定关系",
            businessIdExpression = "#id")
    public Result<Void> delete(@PathVariable @NotNull Long id) {
        boolean success = wechatUserBindingService.deleteWechatUserBinding(id);
        return success ? Result.success() : Result.error("删除失败");
    }

    /**
     * 启用/禁用微信用户绑定关系
     *
     * @param id     记录ID
     * @param status 状态：1-有效，0-无效
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    @OperationLog(businessType = BusinessType.WECHAT_USER_BINDING, operationType = OperationType.UPDATE,
            description = "更新微信用户绑定关系状态",
            businessIdExpression = "#id")
    public Result<Void> updateStatus(@PathVariable @NotNull Long id, @RequestParam @NotNull Integer status) {
        boolean success = wechatUserBindingService.updateStatus(id, status);
        return success ? Result.success() : Result.error("操作失败");
    }

    /**
     * 统计企业绑定关系数量
     *
     * @param partnerId 企业ID
     * @return 绑定关系数量统计
     */
    @GetMapping("/partner/{partnerId}/count")
    public Result<Integer> countByPartnerId(@PathVariable @NotNull Long partnerId) {
        int count = wechatUserBindingService.countByPartnerId(partnerId);
        return Result.success(count);
    }

    /**
     * 统计企业有效绑定关系数量
     *
     * @param partnerId 企业ID
     * @return 有效绑定关系数量统计
     */
    @GetMapping("/partner/{partnerId}/count/valid")
    public Result<Integer> countValidByPartnerId(@PathVariable @NotNull Long partnerId) {
        int count = wechatUserBindingService.countValidByPartnerId(partnerId);
        return Result.success(count);
    }

    /**
     * 根据微信群ID查询绑定关系列表
     *
     * @param chatId 微信群ID
     * @return 绑定关系列表
     */
    @GetMapping("/chat/{chatId}")
    public Result<List<WechatUserBindingVO>> listByChatId(@PathVariable @NotNull String chatId) {
        List<WechatUserBindingVO> voList = wechatUserBindingService.listByChatIdWithGroupName(chatId);
        return Result.success(voList);
    }

    /**
     * 根据企业ID和微信群ID查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @param chatId    微信群ID
     * @return 绑定关系列表
     */
    @GetMapping("/partner/{partnerId}/chat/{chatId}")
    public Result<List<WechatUserBindingVO>> listByPartnerIdAndChatId(@PathVariable @NotNull Long partnerId,
                                                                      @PathVariable @NotNull String chatId) {
        List<WechatUserBindingVO> voList = wechatUserBindingService.listByPartnerIdAndChatIdWithGroupName(partnerId, chatId);
        return Result.success(voList);
    }

    /**
     * 根据企业ID和用户ID查询用户所在的微信群绑定关系
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     * @return 绑定关系列表
     */
    @GetMapping("/partner/{partnerId}/user/{userId}/groups")
    public Result<List<WechatUserBindingVO>> listGroupsByUserId(@PathVariable @NotNull Long partnerId,
                                                                @PathVariable @NotNull Long userId) {
        List<WechatUserBindingVO> voList = wechatUserBindingService.listGroupsByUserIdWithGroupName(partnerId, userId);
        return Result.success(voList);
    }

    /**
     * 根据微信群ID和用户ID查询绑定关系（包含完整用户信息和企业信息）
     *
     * @param chatId 微信群ID
     * @param userId 用户ID
     * @return 绑定关系详情
     */
    @GetMapping("/chat/{chatId}/user/{userId}")
    public Result<WechatUserBindingVO> getByChatIdAndUserId(@PathVariable @NotNull String chatId,
                                                            @PathVariable @NotNull Long userId) {
        WechatUserBindingVO vo = wechatUserBindingService.getByChatIdAndUserIdWithFullInfo(chatId, userId);
        if (vo == null) {
            return Result.error("绑定关系不存在");
        }
        return Result.success(vo);
    }

    /**
     * 根据微信群ID统计绑定关系数量
     *
     * @param chatId 微信群ID
     * @return 绑定关系数量
     */
    @GetMapping("/chat/{chatId}/count")
    public Result<Integer> countByChatId(@PathVariable @NotNull String chatId) {
        int count = wechatUserBindingService.countByChatId(chatId);
        return Result.success(count);
    }

    /**
     * 根据微信群ID统计有效绑定关系数量
     *
     * @param chatId 微信群ID
     * @return 有效绑定关系数量
     */
    @GetMapping("/chat/{chatId}/count/valid")
    public Result<Integer> countValidByChatId(@PathVariable @NotNull String chatId) {
        int count = wechatUserBindingService.countValidByChatId(chatId);
        return Result.success(count);
    }
}
