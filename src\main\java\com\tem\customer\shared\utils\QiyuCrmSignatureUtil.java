package com.tem.customer.shared.utils;

import com.iplatform.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * 七鱼CRM签名验证工具类
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
public class QiyuCrmSignatureUtil {

    /**
     * 验证新版接口认证方式的签名
     * checksum = SHA1(appSecret + md5 + time)
     * 其中md5为请求体的md5哈希值
     * 
     * @param appSecret 应用密钥
     * @param requestBody 请求体内容
     * @param time 时间戳
     * @param checksum 待验证的签名
     * @return 验证结果
     */
    public static boolean validateNewAuthSignature(String appSecret, String requestBody, Long time, String checksum) {
        try {
            if (!StringUtils.hasText(appSecret) || !StringUtils.hasText(checksum) || time == null) {
                LogUtils.warn(log, "七鱼CRM新版认证参数不完整");
                return false;
            }

            // 计算请求体的MD5值
            String requestBodyMd5 = DigestUtils.md5Hex(requestBody != null ? requestBody : "");
            
            // 拼接字符串：appSecret + md5 + time
            String signString = appSecret + requestBodyMd5 + time;
            
            // 计算SHA1哈希值
            String expectedChecksum = DigestUtils.sha1Hex(signString).toLowerCase();
            
            boolean isValid = expectedChecksum.equals(checksum.toLowerCase());
            
            if (!isValid) {
                LogUtils.warn(log, "七鱼CRM新版认证签名验证失败，期望: {}, 实际: {}", expectedChecksum, checksum);
            }
            
            return isValid;
            
        } catch (Exception e) {
            LogUtils.error(log, "验证七鱼CRM新版认证签名异常", e);
            return false;
        }
    }

    /**
     * 验证时间戳的有效性
     * 
     * @param time 请求时间戳
     * @param timeoutMinutes 超时时间（分钟）
     * @return 验证结果
     */
    public static boolean validateTimestamp(Long time, int timeoutMinutes) {
        if (time == null) {
            LogUtils.warn(log, "七鱼CRM请求时间戳为空");
            return false;
        }

        try {
            long currentTime = System.currentTimeMillis() / 1000; // 转换为秒
            long timeDiff = Math.abs(currentTime - time);
            long timeoutSeconds = timeoutMinutes * 60L;

            if (timeDiff > timeoutSeconds) {
                LogUtils.warn(log, "七鱼CRM请求已过期，时间差: {}秒, 超时限制: {}秒", timeDiff, timeoutSeconds);
                return false;
            }

            return true;
        } catch (Exception e) {
            LogUtils.error(log, "验证七鱼CRM请求时间戳异常", e);
            return false;
        }
    }

    /**
     * 生成访问令牌
     * 
     * @param appId 应用ID
     * @param timestamp 时间戳
     * @return 访问令牌
     */
    public static String generateToken(String appId, long timestamp) {
        try {
            String tokenString = appId + "_" + timestamp + "_" + System.nanoTime();
            return DigestUtils.sha1Hex(tokenString);
        } catch (Exception e) {
            LogUtils.error(log, "生成七鱼CRM访问令牌异常", e);
            return null;
        }
    }

    /**
     * 验证AppId和AppSecret
     * 
     * @param appId 应用ID
     * @param appSecret 应用密钥
     * @param configAppId 配置的应用ID
     * @param configAppSecret 配置的应用密钥
     * @return 验证结果
     */
    public static boolean validateAppCredentials(String appId, String appSecret, String configAppId, String configAppSecret) {
        if (!StringUtils.hasText(appId) || !StringUtils.hasText(appSecret)) {
            LogUtils.warn(log, "七鱼CRM应用凭证参数不完整");
            return false;
        }

        if (!StringUtils.hasText(configAppId) || !StringUtils.hasText(configAppSecret)) {
            LogUtils.warn(log, "七鱼CRM应用凭证配置不完整");
            return false;
        }

        boolean isValid = configAppId.equals(appId) && configAppSecret.equals(appSecret);
        
        if (!isValid) {
            LogUtils.warn(log, "七鱼CRM应用凭证验证失败");
        }
        
        return isValid;
    }

    /**
     * 验证新版认证签名
     *
     * @param requestBody 请求体内容
     * @param time 时间戳
     * @param checksum 校验和
     * @param appSecret 应用密钥
     * @return 验证结果
     */
    public static boolean validateNewSignature(String requestBody, Long time, String checksum, String appSecret) {
        try {
            if (!StringUtils.hasText(requestBody) || time == null ||
                !StringUtils.hasText(checksum) || !StringUtils.hasText(appSecret)) {
                LogUtils.warn(log, "新版签名验证参数不完整");
                return false;
            }

            // 计算请求体的MD5
            var md5 = calculateMd5(requestBody);
            if (md5 == null) {
                LogUtils.warn(log, "计算请求体MD5失败");
                return false;
            }

            // 计算期望的checksum: SHA1(appSecret + md5 + time)
            var expectedChecksum = calculateSha1(appSecret + md5 + time);
            if (expectedChecksum == null) {
                LogUtils.warn(log, "计算期望checksum失败");
                return false;
            }

            // 比较checksum
            var isValid = expectedChecksum.equalsIgnoreCase(checksum);

            if (!isValid) {
                LogUtils.warn(log, "新版签名验证失败，期望: {}, 实际: {}", expectedChecksum, checksum);
            }

            return isValid;

        } catch (Exception e) {
            LogUtils.error(log, "新版签名验证异常", e);
            return false;
        }
    }

    /**
     * 计算字符串的MD5哈希值
     *
     * @param input 输入字符串
     * @return MD5哈希值（小写）
     */
    public static String calculateMd5(String input) {
        try {
            if (!StringUtils.hasText(input)) {
                return null;
            }

            var md = MessageDigest.getInstance("MD5");
            var digest = md.digest(input.getBytes(StandardCharsets.UTF_8));

            var sb = new StringBuilder();
            for (var b : digest) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();

        } catch (Exception e) {
            LogUtils.error(log, "计算MD5异常", e);
            return null;
        }
    }

    /**
     * 计算字符串的SHA1哈希值
     *
     * @param input 输入字符串
     * @return SHA1哈希值（小写）
     */
    public static String calculateSha1(String input) {
        try {
            if (!StringUtils.hasText(input)) {
                return null;
            }

            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            byte[] digest = sha1.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();

        } catch (Exception e) {
            LogUtils.error(log, "计算SHA1异常", e);
            return null;
        }
    }

    private QiyuCrmSignatureUtil() {
        // 工具类，禁止实例化
    }
}
