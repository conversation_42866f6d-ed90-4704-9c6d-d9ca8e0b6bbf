package com.tem.customer.controller.qiyu;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoRequest;
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoResponse;
import com.tem.customer.model.dto.qiyu.QiyuTokenResponse;
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoRequest;
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoResponse;
import com.tem.customer.service.qiyu.QiyuCrmService;
import com.tem.customer.service.qiyu.QiyuTokenService;
import com.tem.customer.shared.annotation.QiyuCrmAuth;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 七鱼CRM接口控制器
 * 实现七鱼CRM对接的各个接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/qiyu/crm")
@RequiredArgsConstructor
public class QiyuCrmController {

    private final QiyuTokenService qiyuTokenService;
    private final QiyuCrmService qiyuCrmService;

    /**
     * 获取Token接口
     * GET /get_token
     */
    @GetMapping("/get_token")
    public ResponseEntity<QiyuTokenResponse> getToken(@RequestParam String appid, @RequestParam String appsecret) {
        try {
            LogUtils.info(log, "收到七鱼CRM获取Token请求，AppId: {}", appid);

            QiyuTokenResponse tokenResponse = qiyuTokenService.getToken(appid, appsecret);

            LogUtils.info(log, "七鱼CRM获取Token响应，结果: {}", tokenResponse.getRlt());
            return ResponseEntity.ok(tokenResponse);

        } catch (Exception e) {
            LogUtils.error(log, "七鱼CRM获取Token异常", e);
            return ResponseEntity.ok(QiyuTokenResponse.systemError("System error"));
        }
    }


    /**
     * 微信生态获取用户信息接口
     * POST /get_user_info（在微信生态对接URL中配置）
     */
    @QiyuCrmAuth(description = "微信生态获取用户信息")
    @PostMapping("/wechat/get_user_info")
    public ResponseEntity<QiyuWechatUserInfoResponse> getWechatUserInfo(@RequestBody QiyuWechatUserInfoRequest request) {
        try {
            LogUtils.info(log, "收到七鱼CRM微信生态获取用户信息请求: {}", request);

            // 获取微信用户信息
            QiyuWechatUserInfoResponse wechatUserInfoResponse = qiyuCrmService.getWechatUserInfo(request);

            LogUtils.info(log, "七鱼CRM微信生态获取用户信息响应，结果: {}", wechatUserInfoResponse);
            return ResponseEntity.ok(wechatUserInfoResponse);

        } catch (Exception e) {
            LogUtils.error(log, "七鱼CRM微信生态获取用户信息异常", e);
            QiyuWechatUserInfoResponse errorResponse = new QiyuWechatUserInfoResponse();
            errorResponse.setRlt(500);
            errorResponse.setMessage("System error");
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 企微客服群聊获取群信息接口
     * POST /get_group_info
     */
    @QiyuCrmAuth(description = "企微客服群聊获取群信息")
    @PostMapping("/get_group_info")
    public ResponseEntity<QiyuGroupInfoResponse> getGroupInfo(@RequestBody QiyuGroupInfoRequest request) {
        try {
            LogUtils.info(log, "收到七鱼CRM企微群聊获取群信息请求: {}", request);

            // 获取群聊信息
            QiyuGroupInfoResponse groupInfoResponse = qiyuCrmService.getGroupInfo(request);

            LogUtils.info(log, "七鱼CRM企微群聊获取群信息响应，结果: {}", groupInfoResponse);
            return ResponseEntity.ok(groupInfoResponse);

        } catch (Exception e) {
            LogUtils.error(log, "七鱼CRM企微群聊获取群信息异常", e);
            QiyuGroupInfoResponse errorResponse = new QiyuGroupInfoResponse();
            errorResponse.setRlt(500);
            errorResponse.setMessage("System error");
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 处理OPTIONS预检请求
     * 支持跨域访问
     */
    @RequestMapping(value = {"/wechat/get_user_info", "/get_group_info"}, method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        return ResponseEntity.ok().build();
    }

}
