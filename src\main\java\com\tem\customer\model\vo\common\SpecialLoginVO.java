package com.tem.customer.model.vo.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 代客登录响应VO
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@Accessors(chain = true)
public class SpecialLoginVO {

    /**
     * 登录令牌
     */
    private String alp;

    /**
     * 登录URL
     */
    private String loginUrl;

    /**
     * 服务URL
     */
    private String service;

    /**
     * 登出URL数组
     */
    private String[] logoutUrl;

    /**
     * H5域名URL
     */
    private String h5;

    /**
     * 自动登录URL
     */
    private String autoLoginUrl;

    /**
     * 创建成功响应
     *
     * @param alp           登录令牌
     * @param loginUrl      登录URL
     * @param service       服务URL
     * @param logoutUrl     登出URL数组
     * @param h5            H5域名URL
     * @param autoLoginUrl  自动登录URL
     * @return SpecialLoginVO
     */
    public static SpecialLoginVO success(String alp, String loginUrl, String service, 
                                       String[] logoutUrl, String h5, String autoLoginUrl) {
        return new SpecialLoginVO()
                .setAlp(alp)
                .setLoginUrl(loginUrl)
                .setService(service)
                .setLogoutUrl(logoutUrl)
                .setH5(h5)
                .setAutoLoginUrl(autoLoginUrl);
    }
}
