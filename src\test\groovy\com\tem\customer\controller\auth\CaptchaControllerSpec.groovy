package com.tem.customer.controller.auth

import com.iplatform.common.ResponseDto
import com.iplatform.common.utils.LogUtils
import com.tem.customer.model.dto.SendSmsCodeDTO
import com.tem.customer.model.vo.CaptchaVO
import com.tem.customer.service.auth.CaptchaService
import com.tem.customer.service.auth.LoginAttemptService
import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import com.tem.sso.api.SSOService
import com.tem.sso.dto.VerDto
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.mock.web.MockHttpServletRequest
import jakarta.servlet.http.HttpServletRequest
import java.util.UUID
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

/**
 * 验证码控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class CaptchaControllerSpec extends Specification {

    @SpringBean
    private SSOService ssoService = Mock()
    @SpringBean
    private LoginAttemptService loginAttemptService = Mock()
    @SpringBean
    private CaptchaService captchaService = Mock()
    def captchaController

    def setup() {
        captchaController = new CaptchaController(loginAttemptService, captchaService)
        // 使用反射设置私有字段
        def ssoServiceField = CaptchaController.class.getDeclaredField("ssoService")
        ssoServiceField.setAccessible(true)
        ssoServiceField.set(captchaController, ssoService)
        
        def partnerIdField = CaptchaController.class.getDeclaredField("partnerId")
        partnerIdField.setAccessible(true)
        partnerIdField.set(captchaController, 194L)
        
        def tmcIdField = CaptchaController.class.getDeclaredField("tmcId")
        tmcIdField.setAccessible(true)
        tmcIdField.set(captchaController, 194L)
    }

    def "测试获取图形验证码成功"() {
        given: "准备测试数据和Mock"
        def request = new MockHttpServletRequest()
        request.setRemoteAddr("127.0.0.1")
        
        def expectedCaptchaImage = "base64-image-data"

        and: "Mock服务调用"
        captchaService.checkCaptchaRateLimit("127.0.0.1") >> true
        captchaService.generateCaptchaImage(_) >> expectedCaptchaImage

        when: "调用获取图形验证码接口"
        def result = captchaController.getCaptcha(request)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.captchaKey != null
        result.data.captchaImage == expectedCaptchaImage
        result.data.expireTime == 300
    }

    def "测试获取图形验证码失败 - 频率超限"() {
        given: "准备测试数据和Mock"
        def request = new MockHttpServletRequest()
        request.setRemoteAddr("127.0.0.1")

        and: "Mock频率限制"
        captchaService.checkCaptchaRateLimit("127.0.0.1") >> false

        when: "调用获取图形验证码接口"
        def result = captchaController.getCaptcha(request)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "获取验证码过于频繁，请稍后再试"
    }

    @Unroll
    def "测试获取客户端IP地址 - #scenario"() {
        given: "准备HTTP请求"
        def request = new MockHttpServletRequest()
        
        if (xForwardedFor != null) {
            request.addHeader("X-Forwarded-For", xForwardedFor)
        }
        if (xRealIp != null) {
            request.addHeader("X-Real-IP", xRealIp)
        }
        if (proxyClientIp != null) {
            request.addHeader("Proxy-Client-IP", proxyClientIp)
        }
        if (wlProxyClientIp != null) {
            request.addHeader("WL-Proxy-Client-IP", wlProxyClientIp)
        }
        request.setRemoteAddr(remoteAddr)

        when: "调用私有方法获取客户端IP"
        def method = CaptchaController.class.getDeclaredMethod("getClientIpAddress", HttpServletRequest.class)
        method.setAccessible(true)
        def result = method.invoke(captchaController, request)

        then: "验证结果"
        result == expectedIp

        where:
        scenario                | xForwardedFor      | xRealIp         | proxyClientIp    | wlProxyClientIp  | remoteAddr       | expectedIp
        "X-Forwarded-For"       | "*************"   | null            | null             | null             | "127.0.0.1"      | "*************"
        "X-Real-IP"              | null              | "*************" | null             | null             | "127.0.0.1"      | "*************"
        "Proxy-Client-IP"        | null              | null            | "*************"  | null             | "127.0.0.1"      | "*************"
        "WL-Proxy-Client-IP"     | null              | null            | null             | "*************"  | "127.0.0.1"      | "*************"
        "RemoteAddr"             | null              | null            | null             | null             | "*************"  | "*************"
        "IPv6本地地址"           | null              | null            | null             | null             | "0:0:0:0:0:0:0:1" | "127.0.0.1"
        "IPv6简写本地地址"       | null              | null            | null             | null             | "::1"            | "127.0.0.1"
        "多个X-Forwarded-For"     | "*************,********" | null | null | null | "127.0.0.1" | "*************"
    }

    def "测试发送手机验证码成功"() {
        given: "准备测试数据"
        def sendSmsCodeDTO = new SendSmsCodeDTO()
        sendSmsCodeDTO.setMobile("***********")
        sendSmsCodeDTO.setCaptcha("123456")
        sendSmsCodeDTO.setCaptchaKey("test-captcha-key")

        def rateLimitResult = new CaptchaService.SmsRateLimitResult(true, null)
        def ssoResponse = ResponseDto.success()

        and: "Mock服务调用"
        loginAttemptService.isAccountLocked("***********") >> false
        captchaService.validateCaptcha("test-captcha-key", "123456") >> true
        captchaService.checkSmsRateLimit("***********") >> rateLimitResult
        ssoService.sendVerCode(_) >> ssoResponse

        and: "Mock Dubbo服务字段访问"
        ssoResponse.isSuccess() >> true

        when: "调用发送手机验证码接口"
        def result = captchaController.sendSmsCode(sendSmsCodeDTO)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
    }

    def "测试发送手机验证码失败 - 账户被锁定"() {
        given: "准备测试数据"
        def sendSmsCodeDTO = new SendSmsCodeDTO()
        sendSmsCodeDTO.setMobile("***********")
        sendSmsCodeDTO.setCaptcha("123456")
        sendSmsCodeDTO.setCaptchaKey("test-captcha-key")

        and: "Mock账户锁定状态"
        loginAttemptService.isAccountLocked("***********") >> true
        loginAttemptService.getLockRemainingTime("***********") >> 3000

        when: "调用发送手机验证码接口"
        def result = captchaController.sendSmsCode(sendSmsCodeDTO)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message.contains("账户已锁定")
    }

    def "测试发送手机验证码失败 - 图形验证码错误"() {
        given: "准备测试数据"
        def sendSmsCodeDTO = new SendSmsCodeDTO()
        sendSmsCodeDTO.setMobile("***********")
        sendSmsCodeDTO.setCaptcha("wrong-captcha")
        sendSmsCodeDTO.setCaptchaKey("test-captcha-key")

        and: "Mock服务调用"
        loginAttemptService.isAccountLocked("***********") >> false
        captchaService.validateCaptcha("test-captcha-key", "wrong-captcha") >> false

        when: "调用发送手机验证码接口"
        def result = captchaController.sendSmsCode(sendSmsCodeDTO)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "图形验证码错误"
    }

    def "测试发送手机验证码失败 - 短信频率限制"() {
        given: "准备测试数据"
        def sendSmsCodeDTO = new SendSmsCodeDTO()
        sendSmsCodeDTO.setMobile("***********")
        sendSmsCodeDTO.setCaptcha("123456")
        sendSmsCodeDTO.setCaptchaKey("test-captcha-key")

        def rateLimitResult = new CaptchaService.SmsRateLimitResult(false, "发送过于频繁，请稍后再试")

        and: "Mock服务调用"
        loginAttemptService.isAccountLocked("***********") >> false
        captchaService.validateCaptcha("test-captcha-key", "123456") >> true
        captchaService.checkSmsRateLimit("***********") >> rateLimitResult

        when: "调用发送手机验证码接口"
        def result = captchaController.sendSmsCode(sendSmsCodeDTO)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "发送过于频繁，请稍后再试"
    }

    def "测试发送手机验证码失败 - SSO服务异常"() {
        given: "准备测试数据"
        def sendSmsCodeDTO = new SendSmsCodeDTO()
        sendSmsCodeDTO.setMobile("***********")
        sendSmsCodeDTO.setCaptcha("123456")
        sendSmsCodeDTO.setCaptchaKey("test-captcha-key")

        def rateLimitResult = new CaptchaService.SmsRateLimitResult(true, null)

        and: "Mock服务调用"
        loginAttemptService.isAccountLocked("***********") >> false
        captchaService.validateCaptcha("test-captcha-key", "123456") >> true
        captchaService.checkSmsRateLimit("***********") >> rateLimitResult
        def failedResponse = GroovyMock(ResponseDto)
        failedResponse.isSuccess() >> false
        failedResponse.getCode() >> "500"
        failedResponse.getMsg() >> "短信发送失败"
        ssoService.sendVerCode(_) >> failedResponse

        and: "Mock Dubbo服务字段访问"
        failedResponse.isSuccess() >> false
        failedResponse.getCode() >> "500"
        failedResponse.getMsg() >> "短信发送失败"

        when: "调用发送手机验证码接口"
        def result = captchaController.sendSmsCode(sendSmsCodeDTO)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "短信发送失败，请稍后重试"
    }

    @Unroll
    def "测试发送短信验证码到手机方法 - #scenario"() {
        given: "Mock SSO服务调用"
        ssoService.sendVerCode(_) >> ssoResponse

        when: "调用私有方法"
        def method = CaptchaController.class.getDeclaredMethod("sendSmsCodeToMobile", String.class)
        method.setAccessible(true)
        def result = method.invoke(captchaController, mobile)

        then: "验证结果"
        result == expectedResult

        where:
        scenario        | mobile        | ssoResponse                     | expectedResult
        "发送成功"       | "***********" | ResponseDto.success() | true
        "发送失败"       | "***********" | GroovyMock(ResponseDto) { isSuccess() >> false } | false
        "服务调用异常"   | "***********" | null                            | false
    }
}