package com.tem.customer.model.vo.wechat;

import lombok.Data;

/**
 * 企业微信客户详情VO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class WechatCustomerDetailVO {

    /**
     * 外部联系人ID
     */
    private String externalUserId;

    /**
     * 联系人名称
     */
    private String name;

    /**
     * 联系人头像
     */
    private String avatar;

    /**
     * 联系人类型 1-微信用户 2-企业微信用户
     */
    private Integer type;

    /**
     * 联系人性别 0-未知 1-男性 2-女性
     */
    private Integer gender;

    /**
     * 联系人unionid
     */
    private String unionId;

    /**
     * 联系人备注
     */
    private String remark;

    /**
     * 联系人描述
     */
    private String description;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 职位
     */
    private String position;

    /**
     * 添加该联系人的企业成员用户ID
     */
    private String userId;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 打标签时间
     */
    private Long markTime;

    /**
     * 性别描述
     */
    private String genderDesc;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 获取性别描述
     *
     * @return 性别描述
     */
    public String getGenderDesc() {
        if (gender == null) {
            return "未知";
        }
        return switch (gender) {
            case 1 -> "男性";
            case 2 -> "女性";
            default -> "未知";
        };
    }

    /**
     * 获取类型描述
     *
     * @return 类型描述
     */
    public String getTypeDesc() {
        if (type == null) {
            return "未知";
        }
        return switch (type) {
            case 1 -> "微信用户";
            case 2 -> "企业微信用户";
            default -> "未知";
        };
    }
}