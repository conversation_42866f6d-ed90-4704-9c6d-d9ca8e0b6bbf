package com.tem.customer.service.qiyu

import com.tem.customer.infrastructure.config.QiyuCrmProperties
import com.tem.customer.model.dto.qiyu.QiyuCrmRequest
import com.tem.customer.shared.utils.QiyuCrmSignatureUtil
import org.spockframework.spring.SpringBean
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll
import spock.util.mop.ConfineMetaClassChanges
/**
 * QiyuCrmAuthService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ConfineMetaClassChanges(QiyuCrmSignatureUtil)
class QiyuCrmAuthServiceSpec extends Specification {

    @Subject
    QiyuCrmAuthService qiyuCrmAuthService

    @SpringBean
    QiyuTokenService qiyuTokenService = Mock()

    @SpringBean
    QiyuCrmProperties qiyuCrmProperties = Mock()


    def setup() {
        qiyuCrmAuthService = new QiyuCrmAuthServiceImpl(qiyuTokenService, qiyuCrmProperties)
        
        // 设置默认的配置属性
        def appConfig = new QiyuCrmProperties.App()
        appConfig.setKey("test_app_key")
        appConfig.setSecret("test_app_secret")
        
        def authConfig = new QiyuCrmProperties.Auth()
        authConfig.setTimeTolerance(300)
        
        qiyuCrmProperties.getApp() >> appConfig
        qiyuCrmProperties.getAuth() >> authConfig
    }

    def "验证认证 - 新版认证成功"() {
        given: "请求参数"
        def request = new QiyuCrmRequest()
        def requestBody = '{"test": "data"}'
        def time = System.currentTimeMillis() / 1000 as Long
        
        and: "计算正确的checksum"
        def md5 = java.security.MessageDigest.getInstance("MD5").digest(requestBody.bytes).encodeHex().toString()
        def expectedChecksum = java.security.MessageDigest.getInstance("SHA-1").digest(("test_app_secret" + md5 + time).bytes).encodeHex().toString()

        when: "调用验证认证方法"
        def result = qiyuCrmAuthService.validateAuthentication(request, requestBody, time, expectedChecksum)

        then: "验证结果"
        result
    }

    def "验证认证 - 老版认证成功"() {
        given: "请求参数"
        def request = new QiyuCrmRequest()
        request.setAppid("test_app_key")
        request.setToken("valid_token")
        def requestBody = null
        def time = null
        def checksum = null

        and: "Mock Token验证"
        qiyuTokenService.validateToken("valid_token") >> true

        when: "调用验证认证方法"
        def result = qiyuCrmAuthService.validateAuthentication(request, requestBody, time, checksum)

        then: "验证结果"
        result
    }

    def "验证认证 - 参数不完整"() {
        given: "请求参数不完整"
        def request = new QiyuCrmRequest()
        def requestBody = null
        def time = null
        def checksum = null

        when: "调用验证认证方法"
        def result = qiyuCrmAuthService.validateAuthentication(request, requestBody, time, checksum)

        then: "验证结果"
        !result
    }

    def "验证新版认证 - 时间戳超出容忍范围"() {
        given: "请求参数"
        def requestBody = '{"test": "data"}'
        def time = System.currentTimeMillis() / 1000 - 400 as Long // 超出300秒容忍范围
        def checksum = "valid_checksum"

        when: "调用新版认证方法"
        def result = qiyuCrmAuthService.validateNewAuthentication(requestBody, time, checksum)

        then: "验证结果"
        !result
    }

    def "验证新版认证 - 参数不完整"() {
        given: "请求参数不完整"
        def requestBody = null
        def time = null
        def checksum = null

        when: "调用新版认证方法"
        def result = qiyuCrmAuthService.validateNewAuthentication(requestBody, time, checksum)

        then: "验证结果"
        !result
    }

    def "验证老版认证 - AppId不匹配"() {
        given: "请求参数"
        def request = new QiyuCrmRequest()
        request.setAppid("wrong_app_key")
        request.setToken("valid_token")

        when: "调用老版认证方法"
        def result = qiyuCrmAuthService.validateOldAuthentication(request)

        then: "验证结果"
        !result
    }

    def "验证老版认证 - Token无效"() {
        given: "请求参数"
        def request = new QiyuCrmRequest()
        request.setAppid("test_app_key")
        request.setToken("invalid_token")

        and: "Mock Token验证失败"
        qiyuTokenService.validateToken("invalid_token") >> false

        when: "调用老版认证方法"
        def result = qiyuCrmAuthService.validateOldAuthentication(request)

        then: "验证结果"
        !result
    }

    def "验证老版认证 - 参数不完整"() {
        given: "请求参数不完整"
        def request = new QiyuCrmRequest()
        request.setAppid(null)
        request.setToken(null)

        when: "调用老版认证方法"
        def result = qiyuCrmAuthService.validateOldAuthentication(request)

        then: "验证结果"
        !result
    }

    def "验证Token - 成功"() {
        given: "Mock Token验证成功"
        qiyuTokenService.validateToken("valid_token") >> true

        when: "调用验证Token方法"
        def result = qiyuCrmAuthService.validateToken("test_app_key", "valid_token")

        then: "验证结果"
        result
    }

    def "验证Token - AppId不匹配"() {
        when: "调用验证Token方法，使用错误的AppId"
        def result = qiyuCrmAuthService.validateToken("wrong_app_key", "valid_token")

        then: "验证结果"
        !result
    }

    def "验证Token - Token无效"() {
        given: "Mock Token验证失败"
        qiyuTokenService.validateToken("invalid_token") >> false

        when: "调用验证Token方法"
        def result = qiyuCrmAuthService.validateToken("test_app_key", "invalid_token")

        then: "验证结果"
        !result
    }

    def "验证Token - 参数不完整"() {
        when: "调用验证Token方法，参数不完整"
        def result = qiyuCrmAuthService.validateToken(null, null)

        then: "验证结果"
        !result
    }

    @Unroll
    def "验证认证异常情况 - #scenario"() {
        given: "模拟异常情况"
        if (tokenServiceException) {
            qiyuTokenService.validateToken(_) >> { throw new RuntimeException("Token service error") }
        }

        when: "调用验证认证方法"
        def result = qiyuCrmAuthService.validateAuthentication(request, requestBody, time, checksum)

        then: "异常情况下返回false"
        !result

        where:
        scenario                  | request                   | requestBody | time   | checksum | tokenServiceException
        "新版认证Token服务异常"    | new QiyuCrmRequest()     | "test_body" | 12345L | "checksum"| true
        "老版认证Token服务异常"    | createValidRequest()     | null        | null   | null     | true
    }

    def "完整认证流程测试"() {
        given: "配置新版认证参数"
        def request = new QiyuCrmRequest()
        def requestBody = '{"test": "data"}'
        def time = System.currentTimeMillis() / 1000 as Long
        def md5 = java.security.MessageDigest.getInstance("MD5").digest(requestBody.bytes).encodeHex().toString()
        def checksum = java.security.MessageDigest.getInstance("SHA-1").digest(("test_app_secret" + md5 + time).bytes).encodeHex().toString()

        when: "优先使用新版认证"
        def result = qiyuCrmAuthService.validateAuthentication(request, requestBody, time, checksum)

        then: "验证新版认证被调用"
        result

        when: "配置老版认证参数"
        def oldRequest = new QiyuCrmRequest()
        oldRequest.setAppid("test_app_key")
        oldRequest.setToken("valid_token")
        qiyuTokenService.validateToken("valid_token") >> true

        and: "调用认证（无新版参数，使用老版）"
        def oldResult = qiyuCrmAuthService.validateAuthentication(oldRequest, null, null, null)

        then: "验证老版认证被调用"
        oldResult
    }

    // 辅助方法：创建有效的请求对象
    def createValidRequest() {
        def request = new QiyuCrmRequest()
        request.setAppid("test_app_key")
        request.setToken("valid_token")
        return request
    }
}