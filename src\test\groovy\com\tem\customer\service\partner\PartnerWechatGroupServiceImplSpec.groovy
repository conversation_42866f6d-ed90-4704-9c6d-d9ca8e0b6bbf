package com.tem.customer.service.partner

import com.iplatform.common.ResponseDto
import com.tem.customer.model.vo.partner.PartnerWechatGroupVO
import com.tem.customer.repository.entity.PartnerWechatGroup
import com.tem.customer.repository.mapper.PartnerWechatGroupMapper
import com.tem.customer.shared.exception.BusinessException
import com.tem.platform.api.PartnerService
import com.tem.platform.api.dto.PartnerDto
import spock.lang.Specification
import spock.lang.Subject

/**
 * 企业微信群绑定关系服务实现类测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
class PartnerWechatGroupServiceImplSpec extends Specification {

    @Subject
    PartnerWechatGroupServiceImpl service

    PartnerWechatGroupMapper mapper = Mock()
    PartnerService partnerService = Mock()

    def setup() {
        service = new PartnerWechatGroupServiceImpl()
        service.baseMapper = mapper
        service.partnerService = partnerService
    }

    def "测试根据企业ID查询微信群列表 - 正常情况"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def groups = [
                createPartnerWechatGroup(1L, partnerId, "chat1", "CUSTOMER_SERVICE", 1, 1),
                createPartnerWechatGroup(2L, partnerId, "chat2", "CUSTOMER_SERVICE", 1, 2)
        ]

        when: "调用查询方法"
        def result = service.listByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.selectByPartnerIdOrderBySort(partnerId) >> groups
        result == groups
        result.size() == 2
    }

    def "测试根据企业ID查询微信群列表 - 企业ID为空"() {
        when: "传入空的企业ID"
        def result = service.listByPartnerId(null)

        then: "返回空列表"
        0 * mapper.selectByPartnerIdOrderBySort(_)
        result.isEmpty()
    }

    def "测试根据企业ID查询启用状态的微信群列表"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def enabledGroups = [
                createPartnerWechatGroup(1L, partnerId, "chat1", "CUSTOMER_SERVICE", 1, 1)
        ]

        when: "调用查询启用状态的方法"
        def result = service.listEnabledByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.selectEnabledByPartnerIdOrderBySort(partnerId) >> enabledGroups
        result == enabledGroups
        result.size() == 1
    }

    def "测试统计企业微信群数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 3

        when: "调用统计方法"
        def result = service.countByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.countByPartnerId(partnerId) >> count
        result == count
    }

    def "测试统计启用状态的微信群数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 2

        when: "调用统计启用状态的方法"
        def result = service.countEnabledByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.countEnabledByPartnerId(partnerId) >> count
        result == count
    }

    def "测试根据chatId查询微信群信息"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def group = createPartnerWechatGroup(1L, 1001L, chatId, "CUSTOMER_SERVICE", 1, 1)

        when: "调用查询方法"
        def result = service.getByChatId(chatId)

        then: "验证结果"
        1 * mapper.selectByChatId(chatId) >> group
        result == group
    }

    def "测试根据chatId查询微信群信息 - chatId为空"() {
        when: "传入空的chatId"
        def result = service.getByChatId(null)

        then: "返回null"
        0 * mapper.selectByChatId(_)
        result == null
    }

    def "测试根据chatId查询微信群VO信息 - 成功"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def group = createPartnerWechatGroup(1L, 1001L, chatId, "CUSTOMER_SERVICE", 1, 1)
        def partnerDto = createPartnerDto(1001L, "测试企业")

        and: "Mock依赖方法"
        mapper.selectByChatId(chatId) >> group
        partnerService.findById(1001L) >> ResponseDto.success(partnerDto)

        when: "调用查询VO方法"
        def result = service.getByChatIdVO(chatId)

        then: "验证结果"
        result
        result.chatId == chatId
        result.partnerName == "测试企业"
    }

    def "测试根据chatId查询微信群VO信息 - 群不存在"() {
        given: "准备测试数据"
        def chatId = "chat123"

        and: "Mock群不存在"
        mapper.selectByChatId(chatId) >> null

        when: "调用查询VO方法"
        def result = service.getByChatIdVO(chatId)

        then: "返回null"
        result == null
    }

    def "测试创建企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(null, 1001L, "chat123", "CUSTOMER_SERVICE", null, null)
        
        and: "Mock依赖方法"
        mapper.countByPartnerId(1001L) >> 2
        mapper.existsByChatIdExcludeId("chat123", null) >> false
        mapper.getMaxSortOrderByPartnerId(1001L) >> 2
        mapper.insert(group) >> 1

        when: "调用创建方法"
        def result = service.createPartnerWechatGroup(group)

        then: "验证结果"
        result
        group.status == 1
        group.sortOrder == 3
        group.groupType == "CUSTOMER_SERVICE"
    }

    def "测试创建企业微信群绑定关系 - chatId已存在"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(null, 1001L, "chat123", "CUSTOMER_SERVICE", null, null)
        
        and: "Mock chatId已存在"
        mapper.existsByChatIdExcludeId("chat123", null) >> true

        when: "调用创建方法"
        service.createPartnerWechatGroup(group)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("企业微信群ID已存在")
    }

    def "测试创建企业微信群绑定关系 - 参数校验失败"() {
        when: "传入无效参数"
        service.createPartnerWechatGroup(invalidGroup)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains(expectedMessage)

        where:
        invalidGroup                                                    | expectedMessage
        null                                                           | "不能为空"
        createPartnerWechatGroup(null, null, "chat123", "CUSTOMER_SERVICE", null, null) | "企业ID不能为空"
        createPartnerWechatGroup(null, 1001L, null, "CUSTOMER_SERVICE", null, null)     | "企业微信群ID不能为空"
        createPartnerWechatGroup(null, 1001L, "a" * 65, "CUSTOMER_SERVICE", null, null) | "长度不能超过64个字符"
        createPartnerWechatGroup(null, 1001L, "chat123", "CUSTOMER_SERVICE", null, null, "a" * 501) | "备注长度不能超过500个字符"
    }

    def "测试更新企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(1L, 1001L, "chat123", "CUSTOMER_SERVICE", 1, 1)
        def existingGroup = createPartnerWechatGroup(1L, 1001L, "chat123", "CUSTOMER_SERVICE", 1, 1)
        
        and: "Mock依赖方法"
        mapper.selectById(1L) >> existingGroup
        mapper.existsByChatIdExcludeId("chat123", 1L) >> false
        mapper.updateById(group) >> 1

        when: "调用更新方法"
        def result = service.updatePartnerWechatGroup(group)

        then: "验证结果"
        result
    }

    def "测试更新企业微信群绑定关系 - 记录不存在"() {
        given: "准备测试数据"
        def group = createPartnerWechatGroup(1L, 1001L, "chat123", "测试群", 1, 1)
        
        and: "Mock记录不存在"
        mapper.selectById(1L) >> null

        when: "调用更新方法"
        service.updatePartnerWechatGroup(group)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("不存在")
    }

    def "测试删除企业微信群绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def existingGroup = createPartnerWechatGroup(id, 1001L, "chat123", "CUSTOMER_SERVICE", 1, 1)
        
        and: "Mock依赖方法"
        mapper.selectById(id) >> existingGroup
        mapper.deleteByIdPhysically(id) >> 1

        when: "调用删除方法"
        def result = service.deletePartnerWechatGroup(id)

        then: "验证结果"
        result
    }

    def "测试删除企业微信群绑定关系 - 记录不存在"() {
        given: "准备测试数据"
        def id = 1L
        
        and: "Mock记录不存在"
        mapper.selectById(id) >> null

        when: "调用删除方法"
        service.deletePartnerWechatGroup(id)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("不存在")
    }

    def "测试更新状态 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def status = 0
        
        and: "Mock更新成功"
        mapper.updateById(_) >> 1

        when: "调用更新状态方法"
        def result = service.updateStatus(id, status)

        then: "验证结果"
        result
    }

    def "测试更新状态 - 参数无效"() {
        when: "传入无效参数"
        service.updateStatus(id, status)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains(expectedMessage)

        where:
        id    | status | expectedMessage
        null  | 1      | "记录ID不能为空"
        1L    | null   | "状态值无效"
        1L    | 2      | "状态值无效"
        1L    | -1     | "状态值无效"
    }

    def "测试更新排序 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def sortOrder = 5
        
        and: "Mock更新成功"
        mapper.updateById(_) >> 1

        when: "调用更新排序方法"
        def result = service.updateSortOrder(id, sortOrder)

        then: "验证结果"
        result
    }

    def "测试更新排序 - 参数无效"() {
        when: "传入无效参数"
        service.updateSortOrder(id, sortOrder)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains(expectedMessage)

        where:
        id    | sortOrder | expectedMessage
        null  | 1         | "记录ID不能为空"
        1L    | null      | "排序值不能为空"
        1L    | -1        | "排序值不能为空且不能为负数"
    }

    def "测试检查chatId是否存在"() {
        given: "准备测试数据"
        def chatId = "chat123"
        def excludeId = 1L
        
        when: "调用检查方法"
        def result = service.existsByChatId(chatId, excludeId)

        then: "验证结果"
        1 * mapper.existsByChatIdExcludeId(chatId, excludeId) >> true
        result
    }

    def "测试检查chatId是否存在 - chatId为空"() {
        when: "传入空的chatId"
        def result = service.existsByChatId(null, 1L)

        then: "返回false"
        0 * mapper.existsByChatIdExcludeId(_, _)
        !result
    }

    /**
     * 创建测试用的PartnerWechatGroup对象
     */
    private static PartnerWechatGroup createPartnerWechatGroup(Long id, Long partnerId, String chatId, String groupType, Integer status, Integer sortOrder, String remark = "测试备注") {
        PartnerWechatGroup group = new PartnerWechatGroup()
        group.id = id
        group.partnerId = partnerId
        group.chatId = chatId
        group.groupType = groupType
        group.status = status
        group.sortOrder = sortOrder
        group.remark = remark
        return group
    }

    /**
     * 创建测试用的PartnerDto对象
     */
    private static PartnerDto createPartnerDto(Long id, String name) {
        PartnerDto partnerDto = new PartnerDto()
        partnerDto.id = id
        partnerDto.name = name
        return partnerDto
    }
}