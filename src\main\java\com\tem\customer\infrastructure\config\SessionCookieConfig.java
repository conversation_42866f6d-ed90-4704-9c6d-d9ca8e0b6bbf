package com.tem.customer.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;

/**
 * Spring Session Cookie配置
 * 确保Session Cookie能正确读取和写入
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Configuration
public class SessionCookieConfig {
    
    @Bean
    public CookieSerializer cookieSerializer() {
        DefaultCookieSerializer cookieSerializer = new DefaultCookieSerializer();
        cookieSerializer.setUseHttpOnlyCookie(false);
        cookieSerializer.setSameSite("None");
        cookieSerializer.setUseSecureCookie(true);  // 设置Secure属性
        cookieSerializer.setPartitioned(true);      // 启用CHIPS支持
        cookieSerializer.setUseBase64Encoding(false);
        return cookieSerializer;
    }
}
