package com.tem.customer.controller.wechat;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.wechat.WechatCustomerDetailRequest;
import com.tem.customer.model.dto.wechat.WechatCustomerDetailResponse;
import com.tem.customer.model.vo.wechat.WechatCustomerDetailVO;
import com.tem.customer.service.wechat.WechatApiService;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.converter.WechatCustomerDetailConverter;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 企业微信客户控制器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/wechat/customers")
@RequiredArgsConstructor
@Validated
public class WechatCustomerController {

    private final WechatApiService wechatApiService;
    private final WechatCustomerDetailConverter wechatCustomerDetailConverter;

    /**
     * 获取客户详情
     *
     * @param externalUserId 外部联系人ID
     * @return 客户详情
     */
    @GetMapping("/{externalUserId}/detail")
    public Result<WechatCustomerDetailVO> getCustomerDetail(@PathVariable @NotBlank String externalUserId) {
        LogUtils.info(log, "获取企业微信客户详情，外部联系人ID: {}", externalUserId);

        WechatCustomerDetailResponse response = wechatApiService.getCustomerDetail(externalUserId);
        WechatCustomerDetailVO vo = wechatCustomerDetailConverter.convertToVO(response);

        LogUtils.info(log, "获取企业微信客户详情成功，外部联系人ID: {}, 昵称: {}",
                externalUserId, vo != null ? vo.getName() : "未知");

        return Result.success(vo);
    }

    /**
     * 获取客户详情（POST方式）
     *
     * @param request 客户详情请求
     * @return 客户详情
     */
    @PostMapping("/detail")
    public Result<WechatCustomerDetailVO> getCustomerDetailByPost(@RequestBody @Valid WechatCustomerDetailRequest request) {
        LogUtils.info(log, "获取企业微信客户详情（POST），请求参数: {}", request);

        WechatCustomerDetailResponse response = wechatApiService.getCustomerDetail(request);
        WechatCustomerDetailVO vo = wechatCustomerDetailConverter.convertToVO(response);

        LogUtils.info(log, "获取企业微信客户详情成功，外部联系人ID: {}, 昵称: {}",
                request.getExternalUserId(), vo != null ? vo.getName() : "未知");

        return Result.success(vo);
    }
}