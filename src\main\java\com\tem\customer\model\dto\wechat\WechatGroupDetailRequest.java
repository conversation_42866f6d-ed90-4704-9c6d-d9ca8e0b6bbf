package com.tem.customer.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 企业微信群详情请求
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class WechatGroupDetailRequest {

    /**
     * 客户群ID
     */
    @NotBlank(message = "客户群ID不能为空")
    @JsonProperty("chat_id")
    private String chatId;

    /**
     * 是否需要返回群成员的名字
     * 0-不返回；1-返回。默认不返回
     */
    @JsonProperty("need_name")
    private Integer needName = 1;

    /**
     * 构造函数
     *
     * @param chatId 客户群ID
     */
    public WechatGroupDetailRequest(String chatId) {
        this.chatId = chatId;
        this.needName = 1;
    }

    /**
     * 构造函数
     *
     * @param chatId   客户群ID
     * @param needName 是否需要返回群成员的名字
     */
    public WechatGroupDetailRequest(String chatId, Integer needName) {
        this.chatId = chatId;
        this.needName = needName;
    }

    /**
     * 默认构造函数
     */
    public WechatGroupDetailRequest() {
    }
}
