package com.tem.customer.service.partner

import com.iplatform.common.OrderBizType
import com.iplatform.common.ResponseDto
import com.iplatform.common.utils.DateUtils
import com.tem.customer.shared.exception.BusinessException
import com.tem.oms.api.OrderService
import com.tem.oms.dto.OrderDto
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.UserDto
import spock.lang.Specification
import spock.lang.Subject
/**
 * 企业用户服务实现类测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
class PartnerUserServiceImplSpec extends Specification {

    @Subject
    PartnerUserServiceImpl service

    UserService userService = Mock()
    OrderService orderService = Mock()

    def setup() {
        service = new PartnerUserServiceImpl()
        service.userService = userService
        service.orderService = orderService
    }

    def "测试查询用户订单信息 - 成功"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDtos = [
                createOrderDto(1L, userId, OrderBizType.FLIGHT, "2025-07-30 10:00:00", 10000),
                createOrderDto(2L, userId, OrderBizType.HOTEL, "2025-07-29 10:00:00", 20000)
        ]
        
        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success(orderDtos)

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证结果"
        result.size() == 2
        result[0].id == 1L
        result[0].totalAmount == new BigDecimal("100.00")
        result[1].id == 2L
        result[1].totalAmount == new BigDecimal("200.00")
    }

    def "测试查询用户订单信息 - 参数为空"() {
        when: "传入空参数"
        service.getUserOrders(partnerId, userId)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("不能为空")

        where:
        partnerId | userId
        null      | 2001L
        1001L     | null
        null      | null
    }

    def "测试查询用户订单信息 - 用户不存在"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        and: "Mock用户不存在"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(null)

        when: "调用查询方法"
        service.getUserOrders(partnerId, userId)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("用户信息不存在")
    }

    def "测试查询用户订单信息 - 用户不属于指定企业"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        def userDto = createUserDto(userId, 1002L, "测试用户")
        
        and: "Mock用户属于其他企业"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        when: "调用查询方法"
        service.getUserOrders(partnerId, userId)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("用户不属于指定企业")
    }

    def "测试查询用户订单信息 - 无订单数据"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        def userDto = createUserDto(userId, partnerId, "测试用户")
        
        and: "Mock无订单数据"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success([])

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "返回空列表"
        result.isEmpty()
    }

    def "测试处理订单数据 - 过滤已取消订单"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDtos = [
                createOrderDto(1L, 2001L, OrderBizType.FLIGHT, "2025-07-30 10:00:00", 10000, "CANCELED"),
                createOrderDto(2L, 2001L, OrderBizType.HOTEL, "2025-07-29 10:00:00", 20000, "PAID")
        ]

        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success(orderDtos)

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证结果 - 已取消订单被过滤"
        result.size() == 1
        result[0].id == 2L
    }

    def "测试处理订单数据 - 过滤无行程时间订单"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDtos = [
                createOrderDto(1L, 2001L, OrderBizType.FLIGHT, null, 10000),
                createOrderDto(2L, 2001L, OrderBizType.HOTEL, "2025-07-29 10:00:00", 20000)
        ]

        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success(orderDtos)

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证结果 - 无行程时间订单被过滤"
        result.size() == 1
        result[0].id == 2L
    }

    def "测试处理订单数据 - 排序和数量限制"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDtos = []
        for (i in 1..25) {
            orderDtos.add(createOrderDto(i, 2001L, OrderBizType.FLIGHT, "2025-07-${String.format('%02d', 30-i)} 10:00:00", 10000))
        }

        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success(orderDtos)

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证结果 - 按时间排序且限制数量为20"
        result.size() == 20
        // 验证按时间倒序排列（最新的在前面）
        result[0].travelStartTime.after(result[1].travelStartTime)
    }

    def "测试机票订单转换 - 通过完整流程验证"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDto = createOrderDto(1L, 2001L, OrderBizType.FLIGHT, "2025-07-30 10:00:00", 10000, "PAID")

        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success([orderDto])

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证机票订单转换结果"
        result.size() == 1
        result[0].id == 1L
        result[0].bizType == "10"
        result[0].totalAmount == new BigDecimal("100.00")
    }

    def "测试酒店订单转换 - 通过完整流程验证"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDto = createOrderDto(1L, 2001L, OrderBizType.HOTEL, "2025-07-30 10:00:00", 20000, "PAID")

        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success([orderDto])

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证酒店订单转换结果"
        result.size() == 1
        result[0].id == 1L
        result[0].bizType == "11"
        result[0].totalAmount == new BigDecimal("200.00")
    }

    def "测试火车票订单转换 - 通过完整流程验证"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDto = createOrderDto(1L, 2001L, OrderBizType.TRAIN, "2025-07-30 10:00:00", 5000, "PAID")

        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success([orderDto])

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证火车票订单转换结果"
        result.size() == 1
        result[0].id == 1L
        result[0].bizType == "12"
        result[0].totalAmount == new BigDecimal("50.00")
    }

    /**
     * 创建测试用的UserDto对象
     */
    private UserDto createUserDto(Long id, Long partnerId, String name) {
        UserDto userDto = new UserDto()
        userDto.id = id
        userDto.partnerId = partnerId
        userDto.fullname = name
        userDto.mobile = "13800138000"
        userDto.email = "<EMAIL>"
        return userDto
    }

    /**
     * 创建测试用的OrderDto对象
     */
    private OrderDto createOrderDto(Long id, Long userId, OrderBizType bizType, String travelStartTime, Integer totalAmount, String orderShowStatus = "PAID") {
        OrderDto orderDto = Mock(OrderDto)
        orderDto.getId() >> id
        orderDto.getUserId() >> userId
        orderDto.getBizType() >> bizType.getCode()
        orderDto.getTravelStartTime() >> (travelStartTime ? DateUtils.parse(travelStartTime, "yyyy-MM-dd HH:mm:ss") : null)
        orderDto.getTotalAmount() >> totalAmount
        orderDto.getOrderShowStatus() >> orderShowStatus
        orderDto.getOrderTravellerNames() >> "测试乘客"
        return orderDto
    }

    /**
     * 创建测试用的机票订单Dto对象
     */
    private OrderDto createFlightOrderDto(Long id, Long userId, String travelStartTime, Integer totalAmount, String orderShowStatus = "PAID") {
        OrderDto orderDto = createOrderDto(id, userId, OrderBizType.FLIGHT, travelStartTime, totalAmount, orderShowStatus)

        // 使用Mock对象来模拟复杂的内部结构
        def orderDetail = Mock(Object)
        def flightDetail = Mock(Object)
        flightDetail.getFromCityName() >> "上海"
        flightDetail.getToCityName() >> "北京"
        orderDetail.getFlightOrderDetailDtos() >> [flightDetail]
        orderDto.getOrderDetail() >> orderDetail

        return orderDto
    }

    /**
     * 创建测试用的酒店订单Dto对象
     */
    private OrderDto createHotelOrderDto(Long id, Long userId, String checkInDate, String checkOutDate, Integer totalAmount, String orderShowStatus = "PAID") {
        OrderDto orderDto = createOrderDto(id, userId, OrderBizType.HOTEL, checkInDate, totalAmount, orderShowStatus)

        // 使用Mock对象来模拟复杂的内部结构
        def orderDetail = Mock(Object)
        def hotelDetail = Mock(Object)
        hotelDetail.getHotelName() >> "测试酒店"
        hotelDetail.getHotelAddress() >> "测试地址"
        hotelDetail.getCheckInDate() >> DateUtils.parse(checkInDate, "yyyy-MM-dd HH:mm:ss")
        hotelDetail.getCheckOutDate() >> DateUtils.parse(checkOutDate, "yyyy-MM-dd HH:mm:ss")
        orderDetail.getHotelOrderDetailDto() >> hotelDetail
        orderDto.getOrderDetail() >> orderDetail

        return orderDto
    }

    /**
     * 创建测试用的火车票订单Dto对象
     */
    private OrderDto createTrainOrderDto(Long id, Long userId, String travelStartTime, Integer totalAmount, String orderShowStatus = "PAID") {
        OrderDto orderDto = createOrderDto(id, userId, OrderBizType.TRAIN, travelStartTime, totalAmount, orderShowStatus)

        // 使用Mock对象来模拟复杂的内部结构
        def orderDetail = Mock(Object)
        def trainDetail = Mock(Object)
        trainDetail.getFromStation() >> "上海站"
        trainDetail.getArriveStation() >> "北京站"
        orderDetail.getTrainOrderDetailDtos() >> [trainDetail]
        orderDto.getOrderDetail() >> orderDetail

        return orderDto
    }
}