package com.tem.customer.service.wechat;

import com.iplatform.common.global.cache.CacheItem;
import com.iplatform.common.global.cache.CacheKey;
import com.iplatform.common.global.cache.CacheValue;
import com.iplatform.common.global.cache.GlobalCacheUtil;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.infrastructure.config.WechatApiProperties;
import com.tem.customer.model.dto.wechat.WechatAccessTokenResponse;
import com.tem.customer.model.dto.wechat.WechatCustomerDetailRequest;
import com.tem.customer.model.dto.wechat.WechatCustomerDetailResponse;
import com.tem.customer.model.dto.wechat.WechatGroupDetailRequest;
import com.tem.customer.model.dto.wechat.WechatGroupDetailResponse;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.customer.shared.utils.RestClientUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;

/**
 * 企业微信API服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatApiServiceImpl implements WechatApiService {

    private final WechatApiProperties wechatApiProperties;

    private final RestClientUtils restClientUtils;

    /**
     * Access Token缓存键前缀
     */
    private static final String ACCESS_TOKEN_CACHE_KEY = "wechat:access_token:";

    /**
     * 企业微信API URL常量
     */
    private static final String GET_TOKEN_URL = "/cgi-bin/gettoken";
    private static final String GET_GROUP_DETAIL_URL = "/cgi-bin/externalcontact/groupchat/get";
    private static final String GET_CUSTOMER_DETAIL_URL = "/cgi-bin/externalcontact/get";

    @Override
    public WechatGroupDetailResponse getGroupDetail(WechatGroupDetailRequest request) {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        if (request == null || !StringUtils.hasText(request.getChatId())) {
            throw BusinessException.error("客户群ID不能为空");
        }

        try {
            String accessToken = getAccessToken();
            String uri = GET_GROUP_DETAIL_URL + "?access_token={token}";

            LogUtils.info(log, "调用企业微信获取群详情API，群ID: {}", request.getChatId());

            WechatGroupDetailResponse result = restClientUtils.postJson(
                    uri,
                    new Object[]{accessToken},
                    request,
                    WechatGroupDetailResponse.class
            );

            if (result == null) {
                throw BusinessException.error("企业微信API返回空响应");
            }

            if (!result.isSuccess()) {
                LogUtils.error(log, "企业微信获取群详情失败: {}", result.getErrorMessage());
                throw BusinessException.error("获取群详情失败: " + result.getErrorMessage());
            }

            LogUtils.info(log, "企业微信获取群详情成功，群ID: {}, 群名: {}",
                    request.getChatId(),
                    result.getGroupChat() != null ? result.getGroupChat().getName() : "未知");

            return result;

        } catch (Exception e) {
            LogUtils.error(log, "获取群详情异常", e);
            throw BusinessException.error("获取群详情失败: " + e.getMessage());
        }
    }

    @Override
    public WechatGroupDetailResponse getGroupDetail(String chatId, Integer needName) {
        return getGroupDetail(new WechatGroupDetailRequest(chatId, needName));
    }

    @Override
    public WechatGroupDetailResponse getGroupDetail(String chatId) {
        return getGroupDetail(new WechatGroupDetailRequest(chatId));
    }

    @Override
    public WechatCustomerDetailResponse getCustomerDetail(WechatCustomerDetailRequest request) {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        if (request == null || !StringUtils.hasText(request.getExternalUserId())) {
            throw BusinessException.error("外部联系人ID不能为空");
        }

        try {
            String accessToken = getAccessToken();
            String uri = GET_CUSTOMER_DETAIL_URL + "?access_token={token}";

            LogUtils.info(log, "调用企业微信获取客户详情API，外部联系人ID: {}", request.getExternalUserId());

            WechatCustomerDetailResponse result = restClientUtils.postJson(
                    uri,
                    new Object[]{accessToken},
                    request,
                    WechatCustomerDetailResponse.class
            );

            if (result == null) {
                throw BusinessException.error("企业微信API返回空响应");
            }

            if (!result.isSuccess()) {
                LogUtils.error(log, "企业微信获取客户详情失败: {}", result.getErrorMessage());
                throw BusinessException.error("获取客户详情失败: " + result.getErrorMessage());
            }

            LogUtils.info(log, "企业微信获取客户详情成功，外部联系人ID: {}, 昵称: {}",
                    request.getExternalUserId(),
                    result.getExternalContact() != null ? result.getExternalContact().getName() : "未知");

            return result;

        } catch (Exception e) {
            LogUtils.error(log, "获取客户详情异常", e);
            throw BusinessException.error("获取客户详情失败: " + e.getMessage());
        }
    }

    @Override
    public WechatCustomerDetailResponse getCustomerDetail(String externalUserId) {
        return getCustomerDetail(new WechatCustomerDetailRequest(externalUserId));
    }

    @Override
    public String getAccessToken() {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        String cacheKey = ACCESS_TOKEN_CACHE_KEY + wechatApiProperties.getCorpId();

        // 使用全局缓存获取Access Token
        CacheKey wechatCache = CacheKey.builder()
                .key(cacheKey)
                .allowParallel(false) // 防止多个线程同时获取token
                .autoRefreshTtl(Duration.ofMinutes(5)) // 提前5分钟自动刷新
                .valueSupplier(this::refreshAccessTokenInternal)
                .build();

        try {
            String cachedToken = GlobalCacheUtil.get(wechatCache).getBizValue();
            if (StringUtils.hasText(cachedToken)) {
                LogUtils.debug(log, "从全局缓存获取Access Token成功");
                return cachedToken;
            }
        } catch (Exception e) {
            LogUtils.warn(log, "从全局缓存获取Access Token失败，将重新获取", e);
        }

        return refreshAccessToken();
    }

    @Override
    public String refreshAccessToken() {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        String cacheKey = ACCESS_TOKEN_CACHE_KEY + wechatApiProperties.getCorpId();

        // 使用全局缓存强制更新Access Token
        CacheKey wechatCache = CacheKey.builder()
                .key(cacheKey)
                .allowParallel(false) // 防止多个线程同时获取token
                .autoRefreshTtl(Duration.ofMinutes(5)) // 提前5分钟自动刷新
                .valueSupplier(this::refreshAccessTokenInternal)
                .build();

        try {
            // 先获取缓存项，然后强制更新
            CacheItem cacheItem = GlobalCacheUtil.get(wechatCache);
            String accessToken = GlobalCacheUtil.forceUpdateAndGet(cacheItem).getBizValue();
            LogUtils.info(log, "强制刷新企业微信Access Token成功");
            return accessToken;
        } catch (Exception e) {
            LogUtils.error(log, "强制刷新Access Token异常", e);
            throw BusinessException.error("刷新Access Token失败: " + e.getMessage());
        }
    }

    /**
     * 内部方法：从企业微信API获取Access Token
     *
     * @return CacheValue包装的Access Token
     */
    private CacheValue refreshAccessTokenInternal() {
        if (!StringUtils.hasText(wechatApiProperties.getCorpId()) ||
                !StringUtils.hasText(wechatApiProperties.getCorpSecret())) {
            throw BusinessException.error("企业微信配置不完整，请检查corpId和corpSecret");
        }

        try {
            LogUtils.info(log, "从企业微信API获取Access Token");

            String uri = GET_TOKEN_URL + "?corpid={corpid}&corpsecret={secret}";
            Object[] uriVariables = {wechatApiProperties.getCorpId(), wechatApiProperties.getCorpSecret()};

            WechatAccessTokenResponse result = restClientUtils.get(
                    uri,
                    uriVariables,
                    WechatAccessTokenResponse.class
            );

            if (result == null) {
                throw BusinessException.error("企业微信API返回空响应");
            }

            if (!result.isSuccess()) {
                LogUtils.error(log, "获取Access Token失败: {}", result.getErrorMessage());
                throw BusinessException.error("获取Access Token失败: " + result.getErrorMessage());
            }

            String accessToken = result.getAccessToken();
            if (!StringUtils.hasText(accessToken)) {
                throw BusinessException.error("获取到的Access Token为空");
            }

            // 计算缓存时间，提前5分钟过期
            long cacheTime = Math.max(wechatApiProperties.getTokenCacheTime() - 300, 300);

            LogUtils.info(log, "从企业微信API获取Access Token成功，缓存时间: {}秒", cacheTime);

            // 返回CacheValue对象
            return new CacheValue(accessToken, Duration.ofSeconds(cacheTime));

        } catch (Exception e) {
            LogUtils.error(log, "从企业微信API获取Access Token异常", e);
            throw BusinessException.error("获取Access Token失败: " + e.getMessage());
        }
    }
}
