package com.tem.customer.shared.exception;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.common.Constant;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.common.ResultCode;
import com.tem.customer.shared.utils.TraceIdContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.sql.SQLException;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 全局异常处理器
 * 统一处理应用中的各种异常，返回标准的错误响应格式
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);

        LogUtils.warn(log, "业务异常 - TraceId: {}, URI: {}, Code: {}, Message: {}",
                traceId, request.getRequestURI(), e.getCode(), e.getMessage());

        return Result.<Void>error(e.getCode(), e.getMessage()).traceId(traceId);
    }

    /**
     * 处理参数验证异常 - @Valid注解验证失败
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);

        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        LogUtils.warn(log, "参数验证异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), errorMessage);

        return Result.<Void>error(ResultCode.VALIDATION_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBindException(BindException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);

        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        LogUtils.warn(log, "参数绑定异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), errorMessage);

        return Result.<Void>error(ResultCode.VALIDATION_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理约束验证异常 - @Validated注解验证失败
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);

        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        LogUtils.warn(log, "约束验证异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), errorMessage);

        return Result.<Void>error(ResultCode.VALIDATION_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "缺少必需的请求参数: " + e.getParameterName();

        LogUtils.warn(log, "缺少请求参数异常 - TraceId: {}, URI: {}, Parameter: {}",
                traceId, request.getRequestURI(), e.getParameterName());

        return Result.<Void>error(ResultCode.BAD_REQUEST.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String requiredTypeName = e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "未知类型";
        String errorMessage = "参数类型不匹配: " + e.getName() + " 应该是 " + requiredTypeName + " 类型";

        LogUtils.warn(log, "参数类型不匹配异常 - TraceId: {}, URI: {}, Parameter: {}, RequiredType: {}",
                traceId, request.getRequestURI(), e.getName(), requiredTypeName);

        return Result.<Void>error(ResultCode.BAD_REQUEST.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);

        // 获取详细的错误信息
        String detailMessage = e.getMessage();
        Throwable rootCause = e.getRootCause();
        String rootCauseMessage = rootCause != null ? rootCause.getMessage() : "无根本原因";

        String errorMessage = "请求体格式错误或无法解析";

        // 记录详细的错误信息用于调试
        LogUtils.error(log, "HTTP消息不可读异常详情 - TraceId: {}, URI: {}, DetailMessage: {}, RootCause: {}",
                traceId, request.getRequestURI(), detailMessage, rootCauseMessage, e);

        // 在开发环境下返回详细错误信息
        if (log.isDebugEnabled()) {
            errorMessage = "请求体解析失败: " + (rootCause != null ? rootCauseMessage : detailMessage);
        }

        return Result.<Void>error(ResultCode.BAD_REQUEST.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理不支持的HTTP方法异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "不支持的HTTP方法: " + e.getMethod();

        LogUtils.warn(log, "不支持的HTTP方法异常 - TraceId: {}, URI: {}, Method: {}",
                traceId, request.getRequestURI(), e.getMethod());

        return Result.<Void>error(ResultCode.METHOD_NOT_ALLOWED.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理不支持的媒体类型异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Result<Void> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "不支持的媒体类型: " + e.getContentType();

        LogUtils.warn(log, "不支持的媒体类型异常 - TraceId: {}, URI: {}, ContentType: {}",
                traceId, request.getRequestURI(), e.getContentType());

        return Result.<Void>error(ResultCode.UNSUPPORTED_MEDIA_TYPE.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<Void> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "请求的资源不存在: " + e.getRequestURL();

        LogUtils.warn(log, "404异常 - TraceId: {}, URI: {}", traceId, e.getRequestURL());

        return Result.<Void>error(ResultCode.NOT_FOUND.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    public Result<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "上传文件大小超过限制";

        LogUtils.warn(log, "文件上传大小超限异常 - TraceId: {}, URI: {}, MaxSize: {}",
                traceId, request.getRequestURI(), e.getMaxUploadSize());

        return Result.<Void>error(ResultCode.PAYLOAD_TOO_LARGE.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理数据库重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public Result<Void> handleDuplicateKeyException(DuplicateKeyException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "数据重复，违反唯一性约束";

        LogUtils.warn(log, "数据库重复键异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), e.getMessage());

        return Result.<Void>error(ResultCode.DUPLICATE_KEY_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理数据完整性违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public Result<Void> handleDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "数据完整性约束违反";

        LogUtils.warn(log, "数据完整性违反异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), e.getMessage());

        return Result.<Void>error(ResultCode.DATA_INTEGRITY_VIOLATION.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理SQL异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleSQLException(SQLException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "数据库操作失败";

        LogUtils.error(log, "SQL异常 - TraceId: {}, URI: {}, SQLState: {}, ErrorCode: {}, Message: {}",
                traceId, request.getRequestURI(), e.getSQLState(), e.getErrorCode(), e.getMessage(), e);

        return Result.<Void>error(ResultCode.DATABASE_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "系统内部错误";

        LogUtils.error(log, "空指针异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), e.getMessage(), e);

        return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "参数错误: " + e.getMessage();

        LogUtils.warn(log, "非法参数异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), e.getMessage());

        return Result.<Void>error(ResultCode.BAD_REQUEST.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理非法状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleIllegalStateException(IllegalStateException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "系统状态异常: " + e.getMessage();

        LogUtils.error(log, "非法状态异常 - TraceId: {}, URI: {}, Message: {}",
                traceId, request.getRequestURI(), e.getMessage(), e);

        return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 处理序列化异常
     */
    @ExceptionHandler(SerializationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<Void> handleSerializationException(SerializationException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);

        LogUtils.error(log, "序列化异常 - TraceId: {}, URI: {}, ExceptionType: {}, Message: {}",
                traceId, request.getRequestURI(), e.getClass().getSimpleName(), e.getMessage(), e);

        return Result.<Void>error(ResultCode.UNAUTHORIZED).traceId(traceId);
    }


    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "系统运行时错误";

        LogUtils.error(log, "运行时异常 - TraceId: {}, URI: {}, ExceptionType: {}, Message: {}",
                traceId, request.getRequestURI(), e.getClass().getSimpleName(), e.getMessage(), e);

        return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), errorMessage).traceId(traceId);
    }



    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        String traceId = ensureTraceId(request);
        String errorMessage = "系统内部错误";

        LogUtils.error(log, "未知异常 - TraceId: {}, URI: {}, ExceptionType: {}, Message: {}",
                traceId, request.getRequestURI(), e.getClass().getSimpleName(), e.getMessage(), e);

        return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), errorMessage).traceId(traceId);
    }

    /**
     * 确保有 TraceId，如果没有则从请求头获取或使用请求ID
     * 这是一个兜底方案，用于处理在拦截器执行前发生的异常
     */
    private String ensureTraceId(HttpServletRequest request) {
        String traceId = TraceIdContext.getCurrentTraceId();

        if (traceId == null) {
            // 尝试从请求头获取
            traceId = request.getHeader(Constant.TRACE_ID);

            if (traceId == null) {
                // 使用请求ID作为兜底
                traceId = "REQ-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId();
                log.debug("GlobalExceptionHandler 生成兜底 TraceId: {}", traceId);
            }

            // 设置到 MDC 中，确保后续日志可以使用
            TraceIdContext.setTraceId(traceId);
        }

        return traceId;
    }


}
