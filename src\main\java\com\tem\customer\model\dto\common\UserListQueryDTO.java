package com.tem.customer.model.dto.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 企业用户列表查询请求DTO
 * 用于接收前端查询参数
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@Accessors(chain = true)
public class UserListQueryDTO {

    /**
     * 关键字搜索（用户姓名、手机号、邮箱）
     */
    private String keyword;

    /**
     * 企业ID（可选，如果不传则使用当前用户所属企业）
     */
    private Long partnerId;

    /**
     * 页码（可选，默认不分页）
     */
    private Integer pageNum;

    /**
     * 页大小（可选，默认不分页）
     */
    private Integer pageSize;
}
