package com.tem.customer.shared.utils;

import com.iplatform.common.utils.LogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.util.Map;

/**
 * RestClient工具类
 * 提供便捷的HTTP请求方法，封装常用的请求操作
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RestClientUtils {

    private final RestClient restClient;

    /**
     * GET请求
     *
     * @param uri        请求URI
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T get(String uri, Class<T> responseType) {
        return get(uri, null, null, responseType);
    }

    /**
     * GET请求（带URI变量）
     *
     * @param uri        请求URI
     * @param uriVariables URI变量
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T get(String uri, Object[] uriVariables, Class<T> responseType) {
        return get(uri, uriVariables, null, responseType);
    }

    /**
     * GET请求（带请求头）
     *
     * @param uri        请求URI
     * @param uriVariables URI变量
     * @param headers    请求头
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T get(String uri, Object[] uriVariables,
                    Map<String, String> headers, Class<T> responseType) {
        try {
            RestClient.RequestHeadersUriSpec<?> requestSpec = restClient.get();

            // 设置URI
            RestClient.RequestHeadersSpec<?> headersSpec;
            if (uriVariables != null && uriVariables.length > 0) {
                headersSpec = requestSpec.uri(uri, uriVariables);
            } else {
                headersSpec = requestSpec.uri(uri);
            }

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                headersSpec = headersSpec.headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                });
            }

            T result = headersSpec.retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        LogUtils.error(log, "GET请求失败: {} - {}", response.getStatusCode(), response.getStatusText());
                        throw new RuntimeException("HTTP请求失败: " + response.getStatusCode());
                    })
                    .body(responseType);

            return result;

        } catch (Exception e) {
            LogUtils.error(log, "GET请求异常: {}", uri, e);
            throw new RuntimeException("GET请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST请求（JSON）
     *
     * @param uri        请求URI
     * @param requestBody 请求体对象
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T postJson(String uri, Object requestBody, Class<T> responseType) {
        return postJson(uri, null, requestBody, null, responseType);
    }

    /**
     * POST请求（JSON，带URI变量）
     *
     * @param uri        请求URI
     * @param uriVariables URI变量
     * @param requestBody 请求体对象
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T postJson(String uri, Object[] uriVariables,
                         Object requestBody, Class<T> responseType) {
        return postJson(uri, uriVariables, requestBody, null, responseType);
    }

    /**
     * POST请求（JSON，带请求头）
     *
     * @param uri        请求URI
     * @param uriVariables URI变量
     * @param requestBody 请求体对象
     * @param headers    请求头
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T postJson(String uri, Object[] uriVariables,
                         Object requestBody, Map<String, String> headers, Class<T> responseType) {
        try {
            RestClient.RequestBodyUriSpec requestSpec = restClient.post();

            // 设置URI
            RestClient.RequestBodySpec bodySpec;
            if (uriVariables != null && uriVariables.length > 0) {
                bodySpec = requestSpec.uri(uri, uriVariables);
            } else {
                bodySpec = requestSpec.uri(uri);
            }

            // 设置Content-Type为JSON
            bodySpec = bodySpec.contentType(MediaType.APPLICATION_JSON);

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                bodySpec = bodySpec.headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                });
            }

            // 设置请求体
            String jsonBody;
            if (requestBody instanceof String) {
                jsonBody = (String) requestBody;
            } else {
                jsonBody = JacksonUtils.toJson(requestBody);
            }

            T result = bodySpec.body(jsonBody)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        LogUtils.error(log, "POST请求失败: {} - {}", response.getStatusCode(), response.getStatusText());
                        throw new RuntimeException("HTTP请求失败: " + response.getStatusCode());
                    })
                    .body(responseType);

            return result;

        } catch (Exception e) {
            LogUtils.error(log, "POST请求异常: {}", uri, e);
            throw new RuntimeException("POST请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * PUT请求（JSON）
     *
     * @param uri        请求URI
     * @param requestBody 请求体对象
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T putJson(String uri, Object requestBody, Class<T> responseType) {
        return putJson(uri, null, requestBody, null, responseType);
    }

    /**
     * PUT请求（JSON，带URI变量和请求头）
     *
     * @param uri        请求URI
     * @param uriVariables URI变量
     * @param requestBody 请求体对象
     * @param headers    请求头
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T putJson(String uri, Object[] uriVariables,
                        Object requestBody, Map<String, String> headers, Class<T> responseType) {
        try {
            RestClient.RequestBodyUriSpec requestSpec = restClient.put();

            // 设置URI
            RestClient.RequestBodySpec bodySpec;
            if (uriVariables != null && uriVariables.length > 0) {
                bodySpec = requestSpec.uri(uri, uriVariables);
            } else {
                bodySpec = requestSpec.uri(uri);
            }

            // 设置Content-Type为JSON
            bodySpec = bodySpec.contentType(MediaType.APPLICATION_JSON);

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                bodySpec = bodySpec.headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                });
            }

            // 设置请求体
            String jsonBody;
            if (requestBody instanceof String) {
                jsonBody = (String) requestBody;
            } else {
                jsonBody = JacksonUtils.toJson(requestBody);
            }

            T result = bodySpec.body(jsonBody)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        LogUtils.error(log, "PUT请求失败: {} - {}", response.getStatusCode(), response.getStatusText());
                        throw new RuntimeException("HTTP请求失败: " + response.getStatusCode());
                    })
                    .body(responseType);

            return result;

        } catch (Exception e) {
            LogUtils.error(log, "PUT请求异常: {}", uri, e);
            throw new RuntimeException("PUT请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * DELETE请求
     *
     * @param uri        请求URI
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T delete(String uri, Class<T> responseType) {
        return delete(uri, null, null, responseType);
    }

    /**
     * DELETE请求（带URI变量和请求头）
     *
     * @param uri        请求URI
     * @param uriVariables URI变量
     * @param headers    请求头
     * @param responseType 响应类型
     * @param <T>        响应类型泛型
     * @return 响应对象
     */
    public <T> T delete(String uri, Object[] uriVariables,
                       Map<String, String> headers, Class<T> responseType) {
        try {
            LogUtils.debug(log, "发送DELETE请求: {}", uri);

            RestClient.RequestHeadersUriSpec<?> requestSpec = restClient.delete();

            // 设置URI
            RestClient.RequestHeadersSpec<?> headersSpec;
            if (uriVariables != null && uriVariables.length > 0) {
                headersSpec = requestSpec.uri(uri, uriVariables);
            } else {
                headersSpec = requestSpec.uri(uri);
            }

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                headersSpec = headersSpec.headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                });
            }

            T result = headersSpec.retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        LogUtils.error(log, "DELETE请求失败: {} - {}", response.getStatusCode(), response.getStatusText());
                        throw new RuntimeException("HTTP请求失败: " + response.getStatusCode());
                    })
                    .body(responseType);

            LogUtils.debug(log, "DELETE请求成功: {}", uri);
            return result;

        } catch (Exception e) {
            LogUtils.error(log, "DELETE请求异常: {}", uri, e);
            throw new RuntimeException("DELETE请求失败: " + e.getMessage(), e);
        }
    }
}
