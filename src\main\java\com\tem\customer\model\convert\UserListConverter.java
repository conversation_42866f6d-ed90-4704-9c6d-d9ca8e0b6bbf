package com.tem.customer.model.convert;

import com.tem.customer.model.vo.common.UserListVO;
import com.tem.platform.api.dto.UserBaseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户列表转换器
 * 使用MapStruct进行UserBaseInfo到UserListVO的转换
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Mapper
public interface UserListConverter {

    UserListConverter INSTANCE = Mappers.getMapper(UserListConverter.class);

    /**
     * UserBaseInfo转UserListVO
     * 处理字段类型不匹配问题
     *
     * @param userBaseInfo 用户基础信息
     * @return 用户列表VO
     */
    @Mappings({
            @Mapping(target = "vip", expression = "java(convertBooleanToInteger(userBaseInfo.isVip()))"),
            @Mapping(source = "deptPath", target = "partnerName"),
            @Mapping(target = "partnerCount", ignore = true),
            @Mapping(target = "userCount", ignore = true),
            @Mapping(target = "gender", ignore = true),
            @Mapping(target = "genderDesc", ignore = true),
            @Mapping(target = "orgPathNames", ignore = true),
            @Mapping(target = "empLevelName", ignore = true),
            @Mapping(target = "vipDesc", ignore = true),
            @Mapping(target = "statusDesc", ignore = true),
            @Mapping(target = "typeDesc", ignore = true)
    })
    UserListVO toUserListVO(UserBaseInfo userBaseInfo);

    /**
     * UserBaseInfo列表转UserListVO列表
     *
     * @param userBaseInfoList 用户基础信息列表
     * @return 用户列表VO列表
     */
    List<UserListVO> toUserListVOList(List<UserBaseInfo> userBaseInfoList);

    /**
     * 将boolean类型的vip转换为Integer类型
     *
     * @param vip boolean类型的vip值
     * @return Integer类型的vip值
     */
    default Integer convertBooleanToInteger(boolean vip) {
        return vip ? 1 : 0;
    }
}
