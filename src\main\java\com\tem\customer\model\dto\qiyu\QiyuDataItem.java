package com.tem.customer.model.dto.qiyu;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 七鱼CRM数据项对象
 * 用于构建用户信息、订单信息等数据项
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QiyuDataItem {

    /**
     * 数据项的名称，用于区别不同的数据
     */
    private String key;

    /**
     * 该数据显示的值，类型不做限定，根据实际需要进行设定
     */
    private Object value;

    /**
     * 该项数据显示的名称
     */
    private String label;

    /**
     * 用于排序，显示数据时数据项按index值升序排列
     */
    private Integer index;

    /**
     * 超链接地址。若指定该值，则该项数据将显示为超链接样式
     */
    private String href;

    /**
     * 表示该项数据是否可编辑，true为可编辑，false为不可编辑
     */
    private Boolean edit;

    /**
     * 表明该项数据是否与网易七鱼系统中固定的数据项有映射关系
     * 如真实姓名为real_name，手机号为mobile_phone，电子邮箱为email
     */
    private String map;

    /**
     * 是否将姓名、邮箱、手机号三个保留字段数据保存到网易七鱼系统中
     */
    private Boolean save;

    /**
     * 用于表明该项数据信息的展现位置
     */
    private Boolean zone;

    /**
     * 用于标识该项数据是否为select下拉框
     */
    private Boolean select;

    /**
     * 配合select一起使用，用于标识该select下拉框选项是否被选中
     */
    private Boolean check;

    /**
     * 是否隐藏该数据项
     */
    private Boolean hidden;

    /**
     * 表明是否已在网易七鱼系统中存在自定义字段做保存
     */
    private Boolean isCustomField;

    /**
     * 创建简单数据项
     *
     * @param key 数据项名称
     * @param value 数据项值
     * @param label 显示名称
     * @return 数据项对象
     */
    public static QiyuDataItem simple(String key, Object value, String label) {
        return QiyuDataItem.builder()
                .key(key)
                .value(value)
                .label(label)
                .build();
    }

    /**
     * 创建带自定义字段标识的简单数据项
     *
     * @param key 数据项名称
     * @param value 数据项值
     * @param label 显示名称
     * @param isCustomField 是否为自定义字段
     * @return 数据项对象
     */
    public static QiyuDataItem simpleWithCustomField(String key, Object value, String label, Boolean isCustomField) {
        return QiyuDataItem.builder()
                .key(key)
                .value(value)
                .label(label)
                .isCustomField(isCustomField)
                .build();
    }

    /**
     * 创建带映射的数据项
     * 
     * @param key 数据项名称
     * @param value 数据项值
     * @param label 显示名称
     * @param map 映射字段
     * @return 数据项对象
     */
    public static QiyuDataItem withMap(String key, Object value, String label, String map) {
        return QiyuDataItem.builder()
                .key(key)
                .value(value)
                .label(label)
                .map(map)
                .build();
    }

    /**
     * 创建可编辑数据项
     * 
     * @param key 数据项名称
     * @param value 数据项值
     * @param label 显示名称
     * @param map 映射字段
     * @return 数据项对象
     */
    public static QiyuDataItem editable(String key, Object value, String label, String map) {
        return QiyuDataItem.builder()
                .key(key)
                .value(value)
                .label(label)
                .map(map)
                .edit(true)
                .build();
    }

    /**
     * 创建下拉选择数据项
     * 
     * @param key 数据项名称
     * @param value 选项列表
     * @param label 显示名称
     * @return 数据项对象
     */
    public static QiyuDataItem select(String key, List<SelectOption> value, String label) {
        return QiyuDataItem.builder()
                .key(key)
                .value(value)
                .label(label)
                .select(true)
                .build();
    }

    /**
     * 下拉选择选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectOption {
        /**
         * 选项ID
         */
        private Integer id;

        /**
         * 选项名称
         */
        private String name;

        /**
         * 是否选中
         */
        private Boolean check;
    }
}
