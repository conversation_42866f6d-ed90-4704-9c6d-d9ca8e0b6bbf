package com.tem.customer.controller.partner

import com.tem.customer.BaseControllerSpec
import com.tem.customer.model.convert.WechatUserBindingConverter
import com.tem.customer.model.dto.partner.WechatUserBindingDTO
import com.tem.customer.model.vo.partner.WechatUserBindingVO
import com.tem.customer.repository.entity.WechatUserBinding
import com.tem.customer.service.partner.WechatUserBindingService
import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import com.tem.platform.api.UserService
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDateTime

/**
 * 微信用户绑定关系控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
class PartnerWechatUserBindingControllerSpec extends BaseControllerSpec {

    @Subject
    PartnerWechatUserBindingController partnerWechatUserBindingController

    @MockitoBean
    WechatUserBindingService wechatUserBindingService = Mock()

    @MockitoBean
    UserService userService = Mock()

    def setup() {
        partnerWechatUserBindingController = new PartnerWechatUserBindingController(wechatUserBindingService)
        
        // 通过反射设置Dubbo服务
        setDubboService(partnerWechatUserBindingController, "userService", userService)
    }

    /**
     * 通过反射设置Dubbo服务
     */
    private void setDubboService(Object target, String fieldName, Object service) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, service)
    }

    def "测试根据企业ID查询绑定关系列表 - 正常查询"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def binding1 = createWechatUserBinding(1L, partnerId, 2001L, "union001", "SINGLE_CHAT", 1)
        def binding2 = createWechatUserBinding(2L, partnerId, 2002L, "union002", "GROUP_CHAT", 1)
        def bindings = [binding1, binding2]

        def vo1 = createWechatUserBindingVO(1L, partnerId, 2001L, "union001", "SINGLE_CHAT", 1)
        def vo2 = createWechatUserBindingVO(2L, partnerId, 2002L, "union002", "GROUP_CHAT", 1)
        def voList = [vo1, vo2]

        and: "Mock服务调用"
        wechatUserBindingService.listByPartnerId(partnerId) >> bindings
        wechatUserBindingService.fillUserInfoList(bindings) >> voList

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.listByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        result.data[0].id == 1L
        result.data[0].unionId == "union001"
        result.data[1].id == 2L
        result.data[1].unionId == "union002"
    }

    def "测试根据企业ID查询绑定关系列表 - 空列表"() {
        given: "准备测试数据"
        def partnerId = 1001L

        and: "Mock服务调用返回空列表"
        wechatUserBindingService.listByPartnerId(partnerId) >> []
        wechatUserBindingService.fillUserInfoList([]) >> []

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.listByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.isEmpty()
    }

    def "测试根据企业ID和用户ID查询绑定关系"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def binding = createWechatUserBinding(1L, partnerId, userId, "union001", "SINGLE_CHAT", 1)
        def bindings = [binding]

        def vo = createWechatUserBindingVO(1L, partnerId, userId, "union001", "SINGLE_CHAT", 1)
        def voList = [vo]

        and: "Mock服务调用"
        wechatUserBindingService.listByPartnerIdAndUserId(partnerId, userId) >> bindings
        wechatUserBindingService.fillUserInfoList(bindings) >> voList

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.listByPartnerIdAndUserId(partnerId, userId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 1
        result.data[0].userId == userId
        result.data[0].partnerId == partnerId
    }

    def "测试根据来源类型查询绑定关系列表"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def sourceType = "SINGLE_CHAT"
        def binding = createWechatUserBinding(1L, partnerId, 2001L, "union001", sourceType, 1)
        def bindings = [binding]

        and: "Mock服务调用"
        wechatUserBindingService.listBySourceType(partnerId, sourceType) >> bindings

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.listBySourceType(partnerId, sourceType)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 1
        result.data[0].sourceType == sourceType
    }

    def "测试根据UnionID查询绑定关系详情 - 存在记录"() {
        given: "准备测试数据"
        def unionId = "union001"
        def vo = createWechatUserBindingVO(1L, 1001L, 2001L, unionId, "SINGLE_CHAT", 1)

        and: "Mock服务调用"
        wechatUserBindingService.getByUnionIdWithFullInfo(unionId) >> vo

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.getByUnionId(unionId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.unionId == unionId
    }

    def "测试根据UnionID查询绑定关系详情 - 记录不存在"() {
        given: "准备测试数据"
        def unionId = "nonexistent"

        and: "Mock服务调用返回null"
        wechatUserBindingService.getByUnionIdWithFullInfo(unionId) >> null

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.getByUnionId(unionId)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "微信用户绑定关系不存在"
    }

    def "测试创建微信用户绑定关系 - 成功"() {
        given: "准备测试数据"
        def dto = createWechatUserBindingDTO(null, 1001L, 2001L, "union001", "SINGLE_CHAT", 1)

        and: "Mock服务调用"
        wechatUserBindingService.createWechatUserBinding(_) >> true

        when: "调用创建接口"
        def result = partnerWechatUserBindingController.create(dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
    }

    def "测试创建微信用户绑定关系 - 失败"() {
        given: "准备测试数据"
        def dto = createWechatUserBindingDTO(null, 1001L, 2001L, "union001", "SINGLE_CHAT", 1)

        and: "Mock服务调用返回false"
        wechatUserBindingService.createWechatUserBinding(_) >> false

        when: "调用创建接口"
        def result = partnerWechatUserBindingController.create(dto)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "创建失败"
    }

    def "测试更新微信用户绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def dto = createWechatUserBindingDTO(id, 1001L, 2001L, "union001", "SINGLE_CHAT", 1)

        and: "Mock服务调用"
        wechatUserBindingService.updateWechatUserBinding(_) >> true

        when: "调用更新接口"
        def result = partnerWechatUserBindingController.update(id, dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        
        and: "验证DTO的ID被正确设置"
        dto.id == id
    }

    def "测试更新微信用户绑定关系 - 失败"() {
        given: "准备测试数据"
        def id = 1L
        def dto = createWechatUserBindingDTO(id, 1001L, 2001L, "union001", "SINGLE_CHAT", 1)

        and: "Mock服务调用返回false"
        wechatUserBindingService.updateWechatUserBinding(_) >> false

        when: "调用更新接口"
        def result = partnerWechatUserBindingController.update(id, dto)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "更新失败"
    }

    def "测试删除微信用户绑定关系 - 成功"() {
        given: "准备测试数据"
        def id = 1L

        and: "Mock服务调用"
        wechatUserBindingService.deleteWechatUserBinding(id) >> true

        when: "调用删除接口"
        def result = partnerWechatUserBindingController.delete(id)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
    }

    def "测试删除微信用户绑定关系 - 失败"() {
        given: "准备测试数据"
        def id = 1L

        and: "Mock服务调用返回false"
        wechatUserBindingService.deleteWechatUserBinding(id) >> false

        when: "调用删除接口"
        def result = partnerWechatUserBindingController.delete(id)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "删除失败"
    }

    def "测试更新绑定关系状态 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def status = 0

        and: "Mock服务调用"
        wechatUserBindingService.updateStatus(id, status) >> true

        when: "调用更新状态接口"
        def result = partnerWechatUserBindingController.updateStatus(id, status)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
    }

    def "测试更新绑定关系状态 - 失败"() {
        given: "准备测试数据"
        def id = 1L
        def status = 0

        and: "Mock服务调用返回false"
        wechatUserBindingService.updateStatus(id, status) >> false

        when: "调用更新状态接口"
        def result = partnerWechatUserBindingController.updateStatus(id, status)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "操作失败"
    }

    def "测试统计企业绑定关系数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 5

        and: "Mock服务调用"
        wechatUserBindingService.countByPartnerId(partnerId) >> count

        when: "调用统计接口"
        def result = partnerWechatUserBindingController.countByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == count
    }

    def "测试统计企业有效绑定关系数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 3

        and: "Mock服务调用"
        wechatUserBindingService.countValidByPartnerId(partnerId) >> count

        when: "调用统计接口"
        def result = partnerWechatUserBindingController.countValidByPartnerId(partnerId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == count
    }

    def "测试根据微信群ID查询绑定关系列表"() {
        given: "准备测试数据"
        def chatId = "chat001"
        def vo = createWechatUserBindingVO(1L, 1001L, 2001L, "union001", "GROUP_CHAT", 1)
        vo.setChatId(chatId)
        def voList = [vo]

        and: "Mock服务调用"
        wechatUserBindingService.listByChatIdWithGroupName(chatId) >> voList

        when: "调用查询接口"
        def result = partnerWechatUserBindingController.listByChatId(chatId)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 1
        result.data[0].chatId == chatId
    }

    // ==================== 辅助方法 ====================

    private WechatUserBinding createWechatUserBinding(Long id, Long partnerId, Long userId, 
                                                     String unionId, String sourceType, Integer status) {
        def binding = new WechatUserBinding()
        binding.setId(id)
        binding.setPartnerId(partnerId)
        binding.setUserId(userId)
        binding.setUnionId(unionId)
        binding.setSourceType(sourceType)
        binding.setStatus(status)
        binding.setCreateTime(LocalDateTime.now())
        binding.setUpdateTime(LocalDateTime.now())
        return binding
    }

    private WechatUserBindingVO createWechatUserBindingVO(Long id, Long partnerId, Long userId,
                                                         String unionId, String sourceType, Integer status) {
        def vo = new WechatUserBindingVO()
        vo.setId(id)
        vo.setPartnerId(partnerId)
        vo.setUserId(userId)
        vo.setUnionId(unionId)
        vo.setSourceType(sourceType)
        vo.setStatus(status)
        vo.setCreateTime(LocalDateTime.now())
        vo.setUpdateTime(LocalDateTime.now())
        return vo
    }

    private WechatUserBindingDTO createWechatUserBindingDTO(Long id, Long partnerId, Long userId,
                                                           String unionId, String sourceType, Integer status) {
        def dto = new WechatUserBindingDTO()
        dto.setId(id)
        dto.setPartnerId(partnerId)
        dto.setUserId(userId)
        dto.setUnionId(unionId)
        dto.setSourceType(sourceType)
        dto.setStatus(status)
        return dto
    }
}
