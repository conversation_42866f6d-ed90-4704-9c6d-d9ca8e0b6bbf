package com.tem.customer.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tem.customer.repository.entity.OperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作记录日志Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 根据业务类型和业务ID查询操作日志列表
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 操作日志列表
     */
    List<OperationLog> selectByBusinessTypeAndId(@Param("businessType") String businessType,
                                                 @Param("businessId") Long businessId);

    /**
     * 根据目标企业ID查询操作日志列表
     *
     * @param targetPartnerId 目标企业ID
     * @return 操作日志列表
     */
    List<OperationLog> selectByTargetPartnerId(@Param("targetPartnerId") Long targetPartnerId);

    /**
     * 根据操作人ID查询操作日志列表
     *
     * @param operatorId 操作人ID
     * @return 操作日志列表
     */
    List<OperationLog> selectByOperatorId(@Param("operatorId") Long operatorId);

    /**
     * 分页查询操作日志
     *
     * @param page            分页参数
     * @param businessType    业务类型（可选）
     * @param operationType   操作类型（可选）
     * @param targetPartnerId 目标企业ID（可选）
     * @param operatorId      操作人ID（可选）
     * @param startTime       开始时间（可选）
     * @param endTime         结束时间（可选）
     * @return 分页结果
     */
    IPage<OperationLog> selectPageWithConditions(Page<OperationLog> page,
                                                 @Param("businessType") String businessType,
                                                 @Param("operationType") String operationType,
                                                 @Param("targetPartnerId") Long targetPartnerId,
                                                 @Param("operatorId") Long operatorId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的操作日志数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 操作日志数量
     */
    int countByTimeRange(@Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定时间之前的操作日志
     *
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int deleteByCreateTimeBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 根据业务类型统计操作日志数量
     *
     * @param businessType 业务类型
     * @return 操作日志数量
     */
    int countByBusinessType(@Param("businessType") String businessType);

    /**
     * 根据操作类型统计操作日志数量
     *
     * @param operationType 操作类型
     * @return 操作日志数量
     */
    int countByOperationType(@Param("operationType") String operationType);
}
