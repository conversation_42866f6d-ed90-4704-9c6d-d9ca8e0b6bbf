package com.tem.customer.config;

import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.repository.mapper.WechatUserBindingMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 微信用户绑定表ID生成测试
 * 用于验证修复后的雪花算法ID生成是否正常工作
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
@SpringBootTest
@Transactional
public class WechatUserBindingIdTest {

    @Autowired
    private WechatUserBindingMapper wechatUserBindingMapper;

    /**
     * 测试WechatUserBinding的ID自动生成
     */
    @Test
    public void testWechatUserBindingIdGeneration() {
        log.info("开始测试WechatUserBinding的ID自动生成");

        // 创建测试数据
        WechatUserBinding userBinding = new WechatUserBinding();
        userBinding.setPartnerId(1L);
        userBinding.setUnionId("test_union_id_" + System.currentTimeMillis());
        userBinding.setSourceType("WORK_WECHAT");
        userBinding.setSourceAppId("test_app_id");
        userBinding.setUserId(1L);
        userBinding.setStatus(1);
        userBinding.setRemark("测试雪花算法ID生成");

        // 插入前ID应该为null
        assertNull(userBinding.getId(), "插入前ID应该为null");

        // 执行插入
        int result = wechatUserBindingMapper.insert(userBinding);
        assertEquals(1, result, "插入应该成功");

        // 插入后ID应该被自动生成
        assertNotNull(userBinding.getId(), "插入后ID应该被自动生成");
        assertTrue(userBinding.getId() > 0, "生成的ID应该大于0");

        // 验证ID是否为雪花算法格式（18-19位）
        String idStr = String.valueOf(userBinding.getId());
        assertTrue(idStr.length() >= 18 && idStr.length() <= 19, 
                "雪花算法生成的ID长度应该在18-19位之间，实际长度: " + idStr.length() + "，ID: " + userBinding.getId());

        log.info("WechatUserBinding插入成功，生成的ID: {}，长度: {}", userBinding.getId(), idStr.length());
    }

    /**
     * 测试批量插入时ID的唯一性
     */
    @Test
    public void testBatchInsertIdUniqueness() {
        log.info("开始测试WechatUserBinding批量插入时ID的唯一性");

        // 批量插入多条记录，验证ID的唯一性
        for (int i = 0; i < 5; i++) {
            WechatUserBinding userBinding = new WechatUserBinding();
            userBinding.setPartnerId(1L);
            userBinding.setUnionId("batch_test_union_id_" + System.currentTimeMillis() + "_" + i);
            userBinding.setSourceType("WORK_WECHAT");
            userBinding.setSourceAppId("batch_test_app_id");
            userBinding.setUserId((long) (i + 1));
            userBinding.setStatus(1);
            userBinding.setRemark("批量测试雪花算法ID生成 - " + (i + 1));

            int result = wechatUserBindingMapper.insert(userBinding);
            assertEquals(1, result, "插入应该成功");
            assertNotNull(userBinding.getId(), "ID应该被自动生成");

            // 验证ID格式
            String idStr = String.valueOf(userBinding.getId());
            assertTrue(idStr.length() >= 18 && idStr.length() <= 19, 
                    "雪花算法生成的ID长度应该在18-19位之间");

            log.info("批量插入第{}条记录，生成的ID: {}，长度: {}", i + 1, userBinding.getId(), idStr.length());
        }

        log.info("WechatUserBinding批量插入ID唯一性测试完成");
    }

    /**
     * 测试ID生成的性能
     */
    @Test
    public void testIdGenerationPerformance() {
        log.info("开始测试WechatUserBinding ID生成性能");

        long startTime = System.currentTimeMillis();
        
        // 连续插入10条记录
        for (int i = 0; i < 10; i++) {
            WechatUserBinding userBinding = new WechatUserBinding();
            userBinding.setPartnerId(1L);
            userBinding.setUnionId("perf_test_union_id_" + System.currentTimeMillis() + "_" + i);
            userBinding.setSourceType("WORK_WECHAT");
            userBinding.setSourceAppId("perf_test_app_id");
            userBinding.setUserId((long) (i + 1));
            userBinding.setStatus(1);
            userBinding.setRemark("性能测试 - " + (i + 1));

            wechatUserBindingMapper.insert(userBinding);
            assertNotNull(userBinding.getId(), "ID应该被自动生成");
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        log.info("WechatUserBinding ID生成性能测试完成，插入10条记录耗时: {}ms", duration);
        assertTrue(duration < 5000, "插入10条记录应该在5秒内完成");
    }
}
