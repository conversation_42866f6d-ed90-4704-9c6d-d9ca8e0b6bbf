package com.tem.customer.infrastructure.config;

import com.tem.customer.infrastructure.filter.QiyuCrmAuthFilter;
import com.tem.customer.infrastructure.filter.SessionDeserializationExceptionFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * 过滤器配置类
 * 用于配置自定义过滤器的执行顺序
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Configuration
public class FilterConfig {

    /**
     * 注册Session反序列化异常处理过滤器
     * 设置最高优先级，确保在SessionRepositoryFilter之前执行
     */
    @Bean
    public FilterRegistrationBean<SessionDeserializationExceptionFilter> sessionExceptionFilterRegistration(
            SessionDeserializationExceptionFilter filter) {
        FilterRegistrationBean<SessionDeserializationExceptionFilter> registration =
                new FilterRegistrationBean<>();
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        // 设置最高优先级，确保在所有Spring自动配置的过滤器之前执行
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        registration.setName("sessionDeserializationExceptionFilter");

        log.info("Session反序列化异常处理过滤器已注册，优先级: {}", Ordered.HIGHEST_PRECEDENCE);

        return registration;
    }

    /**
     * 注册七鱼CRM认证过滤器
     * 设置在Spring Security过滤器之前执行，确保能正确处理请求体
     */
    @Bean
    public FilterRegistrationBean<QiyuCrmAuthFilter> qiyuCrmAuthFilterRegistration(
            QiyuCrmAuthFilter filter) {
        FilterRegistrationBean<QiyuCrmAuthFilter> registration =
                new FilterRegistrationBean<>();
        registration.setFilter(filter);
        registration.addUrlPatterns("/api/cx/customer/admin/qiyu/crm/*");
        // 设置在Spring Security过滤器之前执行
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 100);
        registration.setName("qiyuCrmAuthFilter");

        log.info("七鱼CRM认证过滤器已注册，优先级: {}", Ordered.HIGHEST_PRECEDENCE + 100);

        return registration;
    }
}
