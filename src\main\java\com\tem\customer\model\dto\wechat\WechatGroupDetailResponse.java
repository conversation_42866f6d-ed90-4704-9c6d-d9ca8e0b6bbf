package com.tem.customer.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 企业微信群详情响应
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatGroupDetailResponse extends WechatApiResponse {

    /**
     * 客户群详情
     */
    @JsonProperty("group_chat")
    private GroupChat groupChat;

    /**
     * 客户群详情
     */
    @Data
    public static class GroupChat {

        /**
         * 客户群ID
         */
        @JsonProperty("chat_id")
        private String chatId;

        /**
         * 群名
         */
        @JsonProperty("name")
        private String name;

        /**
         * 群主ID
         */
        @JsonProperty("owner")
        private String owner;

        /**
         * 群的创建时间
         */
        @JsonProperty("create_time")
        private Long createTime;

        /**
         * 群公告
         */
        @JsonProperty("notice")
        private String notice;

        /**
         * 群成员列表
         */
        @JsonProperty("member_list")
        private List<Member> memberList;

        /**
         * 群管理员列表
         */
        @JsonProperty("admin_list")
        private List<Admin> adminList;

        /**
         * 当前群成员版本号
         */
        @JsonProperty("member_version")
        private String memberVersion;
    }

    /**
     * 群成员信息
     */
    @Data
    public static class Member {

        /**
         * 群成员id
         */
        @JsonProperty("userid")
        private String userId;

        /**
         * 成员类型。1 - 企业成员；2 - 外部联系人
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * 外部联系人在微信开放平台的唯一身份标识
         */
        @JsonProperty("unionid")
        private String unionId;

        /**
         * 入群时间
         */
        @JsonProperty("join_time")
        private Long joinTime;

        /**
         * 入群方式
         * 1 - 由群成员邀请入群（直接邀请入群）
         * 2 - 由群成员邀请入群（通过邀请链接入群）
         * 3 - 通过扫描群二维码入群
         */
        @JsonProperty("join_scene")
        private Integer joinScene;

        /**
         * 邀请者
         */
        @JsonProperty("invitor")
        private Invitor invitor;

        /**
         * 在群里的昵称
         */
        @JsonProperty("group_nickname")
        private String groupNickname;

        /**
         * 名字
         */
        @JsonProperty("name")
        private String name;
    }

    /**
     * 邀请者信息
     */
    @Data
    public static class Invitor {

        /**
         * 邀请者的userid
         */
        @JsonProperty("userid")
        private String userId;
    }

    /**
     * 群管理员信息
     */
    @Data
    public static class Admin {

        /**
         * 群管理员userid
         */
        @JsonProperty("userid")
        private String userId;
    }
}
