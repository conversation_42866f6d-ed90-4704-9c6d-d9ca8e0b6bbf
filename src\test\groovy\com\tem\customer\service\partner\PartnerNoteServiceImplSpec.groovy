package com.tem.customer.service.partner

import com.tem.customer.repository.entity.PartnerNote
import com.tem.customer.repository.mapper.PartnerNoteMapper
import spock.lang.Specification
import spock.lang.Subject

/**
 * 企业备注服务实现类测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
class PartnerNoteServiceImplSpec extends Specification {

    @Subject
    PartnerNoteServiceImpl service

    PartnerNoteMapper mapper = Mock()

    def setup() {
        service = Spy(PartnerNoteServiceImpl)
        service.baseMapper = mapper
    }

    def "测试根据企业ID查询备注列表 - 正常情况"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def notes = [
                createPartnerNote(1L, partnerId, "备注1", 1),
                createPartnerNote(2L, partnerId, "备注2", 2)
        ]

        when: "调用查询方法"
        def result = service.listByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.selectByPartnerIdOrderBySort(partnerId) >> notes
        result == notes
        result.size() == 2
    }

    def "测试根据企业ID查询备注列表 - 企业ID为空"() {
        when: "传入空的企业ID"
        def result = service.listByPartnerId(null)

        then: "返回空列表"
        0 * mapper.selectByPartnerIdOrderBySort(_)
        result.isEmpty()
    }

    def "测试添加企业备注 - 成功"() {
        given: "准备测试数据"
        def note = createPartnerNote(null, 1001L, "测试备注", null)
        
        and: "Mock依赖方法"
        mapper.countByPartnerId(1001L) >> 2
        mapper.getMaxSortOrderByPartnerId(1001L) >> 3
        mapper.insert(note) >> 1

        when: "调用添加方法"
        def result = service.addPartnerNote(note)

        then: "验证结果"
        result
        result.sortOrder == 4
    }

    def "测试添加企业备注 - 参数为空"() {
        when: "传入空参数"
        def result = service.addPartnerNote(null)

        then: "返回null"
        0 * mapper.countByPartnerId(_)
        result == null
    }

    def "测试添加企业备注 - 企业ID为空"() {
        given: "准备测试数据"
        def note = createPartnerNote(null, null, "测试备注", null)

        when: "调用添加方法"
        def result = service.addPartnerNote(note)

        then: "返回null"
        0 * mapper.countByPartnerId(_)
        result == null
    }

    def "测试添加企业备注 - 数量超限"() {
        given: "准备测试数据"
        def note = createPartnerNote(null, 1001L, "测试备注", null)
        
        and: "Mock数量已达上限"
        mapper.countByPartnerId(1001L) >> 8

        when: "调用添加方法"
        def result = service.addPartnerNote(note)

        then: "返回null"
        result == null
    }

    def "测试更新企业备注 - 成功"() {
        given: "准备测试数据"
        def note = createPartnerNote(1L, 1001L, "更新后的备注", 1)
        
        and: "Mock更新成功"
        mapper.updateById(note) >> 1

        when: "调用更新方法"
        def result = service.updatePartnerNote(note)

        then: "验证结果"
        result
    }

    def "测试更新企业备注 - 参数为空"() {
        when: "传入空参数"
        def result = service.updatePartnerNote(null)

        then: "返回false"
        0 * mapper.updateById(_)
        !result
    }

    def "测试更新企业备注 - ID为空"() {
        given: "准备测试数据"
        def note = createPartnerNote(null, 1001L, "更新后的备注", 1)

        when: "调用更新方法"
        def result = service.updatePartnerNote(note)

        then: "返回false"
        0 * mapper.updateById(_)
        !result
    }

    def "测试删除企业备注 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        
        and: "Mock删除成功"
        mapper.deleteById(id) >> 1

        when: "调用删除方法"
        def result = service.deletePartnerNote(id)

        then: "验证结果"
        result
    }

    def "测试删除企业备注 - ID为空"() {
        when: "传入空ID"
        def result = service.deletePartnerNote(null)

        then: "返回false"
        0 * mapper.deleteById(_)
        !result
    }

    def "测试检查企业备注数量是否超过限制 - 未超限"() {
        given: "准备测试数据"
        def partnerId = 1001L
        
        and: "Mock数量未超限"
        mapper.countByPartnerId(partnerId) >> 5

        when: "调用检查方法"
        def result = service.isExceedLimit(partnerId)

        then: "验证结果"
        !result
    }

    def "测试检查企业备注数量是否超过限制 - 已超限"() {
        given: "准备测试数据"
        def partnerId = 1001L
        
        and: "Mock数量已超限"
        mapper.countByPartnerId(partnerId) >> 8

        when: "调用检查方法"
        def result = service.isExceedLimit(partnerId)

        then: "验证结果"
        result
    }

    def "测试检查企业备注数量是否超过限制 - 企业ID为空"() {
        when: "传入空的企业ID"
        def result = service.isExceedLimit(null)

        then: "返回true"
        0 * mapper.countByPartnerId(_)
        result
    }

    def "测试统计企业备注数量"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def count = 3

        when: "调用统计方法"
        def result = service.countByPartnerId(partnerId)

        then: "验证结果"
        1 * mapper.countByPartnerId(partnerId) >> count
        result == count
    }

    def "测试统计企业备注数量 - 企业ID为空"() {
        when: "传入空的企业ID"
        def result = service.countByPartnerId(null)

        then: "返回0"
        0 * mapper.countByPartnerId(_)
        result == 0
    }

    def "测试调整备注排序位置 - 成功"() {
        given: "准备测试数据"
        def id = 1L
        def targetPosition = 2
        def partnerId = 1001L
        
        def allNotes = [
                createPartnerNote(1L, partnerId, "备注1", 1),
                createPartnerNote(2L, partnerId, "备注2", 2),
                createPartnerNote(3L, partnerId, "备注3", 3)
        ]

        when: "调用调整排序方法"
        def result = service.updateSortPosition(id, targetPosition)

        then: "Mock依赖方法"
        1 * service.getById(id) >> allNotes[0]
        1 * service.listByPartnerId(partnerId) >> allNotes
        // 验证updateById被调用了3次（每个备注都要更新排序值）
        3 * service.updateById(_) >> true

        and: "验证结果"
        result
    }

    def "测试调整备注排序位置 - 参数无效"() {
        when: "传入无效参数"
        def result = service.updateSortPosition(id, targetPosition)

        then: "返回false"
        0 * mapper.selectById(_)
        !result

        where:
        id    | targetPosition
        null  | 1
        1L    | null
        1L    | 0
        1L    | -1
    }

    def "测试调整备注排序位置 - 备注不存在"() {
        given: "准备测试数据"
        def id = 1L
        def targetPosition = 2
        
        and: "Mock备注不存在"
        mapper.selectById(id) >> null

        when: "调用调整排序方法"
        def result = service.updateSortPosition(id, targetPosition)

        then: "返回false"
        !result
    }

    def "测试调整备注排序位置 - 目标位置超出范围"() {
        given: "准备测试数据"
        def id = 1L
        def targetPosition = 5
        def partnerId = 1001L
        
        def allNotes = [
                createPartnerNote(1L, partnerId, "备注1", 1),
                createPartnerNote(2L, partnerId, "备注2", 2),
                createPartnerNote(3L, partnerId, "备注3", 3)
        ]

        and: "Mock依赖方法"
        mapper.selectById(id) >> allNotes[0]
        mapper.selectByPartnerIdOrderBySort(partnerId) >> allNotes

        when: "调用调整排序方法"
        def result = service.updateSortPosition(id, targetPosition)

        then: "返回false"
        !result
    }

    def "测试调整备注排序位置 - 位置无变化"() {
        given: "准备测试数据"
        def id = 1L
        def targetPosition = 1
        def partnerId = 1001L
        
        def allNotes = [
                createPartnerNote(1L, partnerId, "备注1", 1),
                createPartnerNote(2L, partnerId, "备注2", 2),
                createPartnerNote(3L, partnerId, "备注3", 3)
        ]

        and: "Mock依赖方法"
        mapper.selectById(id) >> allNotes[0]
        mapper.selectByPartnerIdOrderBySort(partnerId) >> allNotes

        when: "调用调整排序方法"
        def result = service.updateSortPosition(id, targetPosition)

        then: "返回true"
        result
        0 * mapper.updateById(_)
    }

    /**
     * 创建测试用的PartnerNote对象
     */
    private static PartnerNote createPartnerNote(Long id, Long partnerId, String content, Integer sortOrder) {
        PartnerNote note = new PartnerNote()
        note.id = id
        note.partnerId = partnerId
        note.content = content
        note.sortOrder = sortOrder
        note.title = "测试标题"
        return note
    }
}