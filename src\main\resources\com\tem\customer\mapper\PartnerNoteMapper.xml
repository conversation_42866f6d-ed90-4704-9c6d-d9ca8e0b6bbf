<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tem.customer.repository.mapper.PartnerNoteMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tem.customer.repository.entity.PartnerNote">
        <id column="id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        partner_id,
        title,
        content,
        sort_order,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version
    </sql>

    <!-- 根据企业ID查询备注列表，按排序字段升序排列 -->
    <select id="selectByPartnerIdOrderBySort" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_partner_note
        WHERE partner_id = #{partnerId}
          AND deleted = 0
        ORDER BY sort_order, create_time
    </select>

    <!-- 统计企业的备注数量 -->
    <select id="countByPartnerId" resultType="int">
        SELECT COUNT(1)
        FROM t_partner_note
        WHERE partner_id = #{partnerId}
          AND deleted = 0
    </select>

    <!-- 获取企业备注的最大排序值 -->
    <select id="getMaxSortOrderByPartnerId" resultType="java.lang.Integer">
        SELECT MAX(sort_order)
        FROM t_partner_note
        WHERE partner_id = #{partnerId}
          AND deleted = 0
    </select>
</mapper>
