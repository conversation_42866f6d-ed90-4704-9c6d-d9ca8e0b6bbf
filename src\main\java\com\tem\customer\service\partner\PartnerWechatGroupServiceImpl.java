package com.tem.customer.service.partner;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.convert.PartnerWechatGroupConverter;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.model.vo.partner.PartnerWechatGroupVO;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.customer.repository.mapper.PartnerWechatGroupMapper;
import com.tem.platform.api.PartnerService;
import com.tem.platform.api.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 企业微信群绑定关系服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service("partnerWechatGroupService")
public class PartnerWechatGroupServiceImpl extends ServiceImpl<PartnerWechatGroupMapper, PartnerWechatGroup> implements PartnerWechatGroupService {

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;

    @Override
    public List<PartnerWechatGroup> listByPartnerId(Long partnerId) {
        if (partnerId == null) {
            LogUtils.warn(log, "查询企业微信群时，企业ID为空");
            return List.of();
        }
        return baseMapper.selectByPartnerIdOrderBySort(partnerId);
    }

    @Override
    public List<PartnerWechatGroup> listEnabledByPartnerId(Long partnerId) {
        if (partnerId == null) {
            LogUtils.warn(log, "查询启用状态企业微信群时，企业ID为空");
            return List.of();
        }
        return baseMapper.selectEnabledByPartnerIdOrderBySort(partnerId);
    }

    @Override
    public int countByPartnerId(Long partnerId) {
        if (partnerId == null) {
            return 0;
        }
        return baseMapper.countByPartnerId(partnerId);
    }

    @Override
    public int countEnabledByPartnerId(Long partnerId) {
        if (partnerId == null) {
            return 0;
        }
        return baseMapper.countEnabledByPartnerId(partnerId);
    }

    @Override
    public PartnerWechatGroup getByChatId(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            LogUtils.warn(log, "根据chatId查询企业微信群时，chatId为空");
            return null;
        }
        return baseMapper.selectByChatId(chatId);
    }

    @Override
    public PartnerWechatGroupVO getByChatIdVO(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            LogUtils.warn(log, "根据chatId查询企业微信群时，chatId为空");
            return null;
        }

        // 查询绑定关系
        PartnerWechatGroup group = baseMapper.selectByChatId(chatId);
        if (group == null) {
            LogUtils.warn(log, "根据chatId未找到企业微信群绑定关系，chatId: {}", chatId);
            return null;
        }

        // 转换为VO对象
        PartnerWechatGroupVO vo = PartnerWechatGroupConverter.INSTANCE.toVO(group);

        // 获取企业信息并设置企业名称
        if (group.getPartnerId() != null) {
            try {
                ResponseDto<PartnerDto> partnerResponse = partnerService.findById(group.getPartnerId());
                if (partnerResponse != null && partnerResponse.isSuccess() && partnerResponse.getData() != null) {
                    PartnerDto partnerDto = partnerResponse.getData();
                    vo.setPartnerName(partnerDto.getName());
                    LogUtils.debug(log, "获取企业信息成功，企业ID: {}, 企业名称: {}",
                        group.getPartnerId(), partnerDto.getName());
                } else {
                    LogUtils.warn(log, "获取企业信息失败，企业ID: {}, 响应: {}",
                        group.getPartnerId(), partnerResponse);
                }
            } catch (Exception e) {
                LogUtils.error(log, "调用PartnerService获取企业信息异常，企业ID: {}",
                    group.getPartnerId(), e);
            }
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PartnerWechatGroup createPartnerWechatGroup(PartnerWechatGroup partnerWechatGroup) {
        // 参数校验
        validatePartnerWechatGroup(partnerWechatGroup, false);

        // 检查chatId是否已存在
        if (existsByChatId(partnerWechatGroup.getChatId(), null)) {
            throw BusinessException.error("企业微信群ID已存在，不能重复绑定");
        }

        // 设置默认值
        if (partnerWechatGroup.getStatus() == null) {
            partnerWechatGroup.setStatus(PartnerWechatGroup.Status.ENABLED.getCode());
        }
        if (partnerWechatGroup.getSortOrder() == null) {
            Integer maxSortOrder = baseMapper.getMaxSortOrderByPartnerId(partnerWechatGroup.getPartnerId());
            partnerWechatGroup.setSortOrder(maxSortOrder == null ? 1 : maxSortOrder + 1);
        }
        if (!StringUtils.hasText(partnerWechatGroup.getGroupType())) {
            partnerWechatGroup.setGroupType(PartnerWechatGroup.GroupType.CUSTOMER_SERVICE.getCode());
        }

        boolean success = save(partnerWechatGroup);
        if (success) {
            LogUtils.info(log, "创建企业微信群绑定关系成功，企业ID：{}，群ID：{}", 
                partnerWechatGroup.getPartnerId(), partnerWechatGroup.getChatId());
            return partnerWechatGroup;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePartnerWechatGroup(PartnerWechatGroup partnerWechatGroup) {
        // 参数校验
        validatePartnerWechatGroup(partnerWechatGroup, true);

        // 检查记录是否存在
        PartnerWechatGroup existing = getById(partnerWechatGroup.getId());
        if (existing == null) {
            throw BusinessException.error("企业微信群绑定关系不存在");
        }

        // 检查chatId是否已存在（排除当前记录）
        if (existsByChatId(partnerWechatGroup.getChatId(), partnerWechatGroup.getId())) {
            throw BusinessException.error("企业微信群ID已存在，不能重复绑定");
        }

        boolean result = updateById(partnerWechatGroup);
        if (result) {
            LogUtils.info(log, "更新企业微信群绑定关系成功，记录ID：{}，群ID：{}", 
                partnerWechatGroup.getId(), partnerWechatGroup.getChatId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePartnerWechatGroup(Long id) {
        if (id == null) {
            throw BusinessException.error("记录ID不能为空");
        }

        PartnerWechatGroup existing = getById(id);
        if (existing == null) {
            throw BusinessException.error("企业微信群绑定关系不存在");
        }

        // 使用物理删除
        int deletedRows = baseMapper.deleteByIdPhysically(id);
        boolean result = deletedRows > 0;
        if (result) {
            LogUtils.info(log, "物理删除企业微信群绑定关系成功，记录ID：{}，群ID：{}",
                id, existing.getChatId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        if (id == null) {
            throw BusinessException.error("记录ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw BusinessException.error("状态值无效，必须为0（禁用）或1（启用）");
        }

        PartnerWechatGroup partnerWechatGroup = new PartnerWechatGroup();
        partnerWechatGroup.setId(id);
        partnerWechatGroup.setStatus(status);

        boolean result = updateById(partnerWechatGroup);
        if (result) {
            LogUtils.info(log, "更新企业微信群状态成功，记录ID：{}，状态：{}", id, status);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(Long id, Integer sortOrder) {
        if (id == null) {
            throw BusinessException.error("记录ID不能为空");
        }
        if (sortOrder == null || sortOrder < 0) {
            throw BusinessException.error("排序值不能为空且不能为负数");
        }

        PartnerWechatGroup partnerWechatGroup = new PartnerWechatGroup();
        partnerWechatGroup.setId(id);
        partnerWechatGroup.setSortOrder(sortOrder);

        boolean result = updateById(partnerWechatGroup);
        if (result) {
            LogUtils.info(log, "更新企业微信群排序成功，记录ID：{}，排序值：{}", id, sortOrder);
        }
        return result;
    }

    @Override
    public boolean existsByChatId(String chatId, Long excludeId) {
        if (!StringUtils.hasText(chatId)) {
            return false;
        }
        return baseMapper.existsByChatIdExcludeId(chatId, excludeId);
    }

    /**
     * 校验企业微信群绑定关系参数
     *
     * @param partnerWechatGroup 企业微信群绑定关系
     * @param isUpdate 是否为更新操作
     */
    private void validatePartnerWechatGroup(PartnerWechatGroup partnerWechatGroup, boolean isUpdate) {
        if (partnerWechatGroup == null) {
            throw BusinessException.error("企业微信群绑定关系不能为空");
        }
        if (isUpdate && partnerWechatGroup.getId() == null) {
            throw BusinessException.error("更新时记录ID不能为空");
        }
        if (partnerWechatGroup.getPartnerId() == null) {
            throw BusinessException.error("企业ID不能为空");
        }
        if (!StringUtils.hasText(partnerWechatGroup.getChatId())) {
            throw BusinessException.error("企业微信群ID不能为空");
        }
        if (partnerWechatGroup.getChatId().length() > 64) {
            throw BusinessException.error("企业微信群ID长度不能超过64个字符");
        }
        if (partnerWechatGroup.getRemark() != null && partnerWechatGroup.getRemark().length() > 500) {
            throw BusinessException.error("备注长度不能超过500个字符");
        }
    }
}
