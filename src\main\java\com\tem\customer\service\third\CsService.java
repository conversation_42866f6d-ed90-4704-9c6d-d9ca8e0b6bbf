package com.tem.customer.service.third;

import com.google.common.collect.Maps;
import com.iplatform.common.OrderBizType;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.DateUtils;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.common.OrderInfoDto;
import com.tem.customer.model.from.UserAndStandardForm;
import com.tem.errand.quark.api.PartnerRankService;
import com.tem.oms.api.OrderService;
import com.tem.oms.dto.OrderDto;
import com.tem.oms.enums.ShowStatusEnum;
import com.tem.platform.api.DictService;
import com.tem.platform.api.OrgService;
import com.tem.platform.api.PartnerService;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.OrgDto;
import com.tem.platform.api.dto.PartnerDto;
import com.tem.platform.api.dto.UserDto;
import com.tem.platform.api.dto.UserPartnerDto;
import com.tem.platform.api.dto.UserTagDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CsService {


    @DubboReference(timeout = 10000, retries = 0, check = false)
    private OrderService orderService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private OrgService orgService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private DictService dictService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerRankService partnerRankService;

    private static final String CANCELED = "CANCELED";

    /**
     * 行程信息显示规则
     * 行程信息：出发城市，到达城市，多程信息（字数根据单元格长度控制）
     * 例子：1.上海 -> 北京，2.北京 -> 天津 显示为 上海-北京-天津的形式
     * 例子：1.上海 -> 北京，2.天津 -> 杭州 3.上海-北京 显示为 上海-北京，天津-杭州，上海-北京
     */
    private static final BinaryOperator<String> ACCUMULATOR = (v1, v2) -> {
        if (StringUtils.isEmpty(v1)) {
            return v2;
        }
        String[] item = StringUtils.split(v1, ",");
        String[] lastOfItem = StringUtils.split(item[item.length - 1], "-");
        String lastElement = lastOfItem[lastOfItem.length - 1];
        String[] nextItem = StringUtils.split(v2, "-");
        String startElement = nextItem[0];
        if (StringUtils.equals(lastElement, startElement)) {
            return v1 + "-" + nextItem[nextItem.length - 1];
        }
        return v1 + "," + v2;
    };

    private static final BiConsumer<OrderInfoDto, OrderDto> FLIGHT = (infoDto, order) -> {
        Optional.ofNullable(order.getOrderDetail())
                .flatMap(orderDetailDto -> Optional.ofNullable(orderDetailDto.getFlightOrderDetailDtos()))
                .ifPresent(flightOrderDetailDtos -> {
                    String reduce = flightOrderDetailDtos.stream()
                            .map(flightOrderDetailDto -> String.format("%s-%s", flightOrderDetailDto.getFromCityName(), flightOrderDetailDto.getToCityName()))
                            .reduce("", ACCUMULATOR);
                    infoDto.setTrip(reduce);
                });
        Optional.ofNullable(order.getOrderShowStatus())
                .ifPresent(orderShowStatus -> infoDto.setOrderShowStatus(ShowStatusEnum.valueOf(orderShowStatus).getFlightMessage()));
    };

    private static final BiConsumer<OrderInfoDto, OrderDto> INTL_FLIGHT = (infoDto, order) -> {
        Optional.ofNullable(order.getOrderDetail())
                .flatMap(orderDetailDto -> Optional.ofNullable(orderDetailDto.getIntlFlightOrderDetailDtos()))
                .ifPresent(intlFlightOrderDetailDtos -> {
                    String reduce = intlFlightOrderDetailDtos.stream()
                            .map(intlFlightOrderDetailDto -> String.format("%s-%s", intlFlightOrderDetailDto.getFromCityName(), intlFlightOrderDetailDto.getToCityName()))
                            .reduce("", ACCUMULATOR);
                    infoDto.setTrip(reduce);
                });
        Optional.ofNullable(order.getOrderShowStatus())
                .ifPresent(orderShowStatus -> infoDto.setOrderShowStatus(ShowStatusEnum.valueOf(orderShowStatus).getIntFlightMessage()));
    };

    private static final BiConsumer<OrderInfoDto, OrderDto> TRAIN = (infoDto, order) -> {
        Optional.ofNullable(order.getOrderDetail())
                .flatMap(orderDetailDto -> Optional.ofNullable(orderDetailDto.getTrainOrderDetailDtos()))
                .flatMap(trainOrderDetailDtos -> trainOrderDetailDtos.stream().findFirst())
                .ifPresent(trainOrderDetailDto -> infoDto.setTrip(String.format("%s-%s", trainOrderDetailDto.getFromStation(), trainOrderDetailDto.getArriveStation())));
        Optional.ofNullable(order.getOrderShowStatus())
                .ifPresent(orderShowStatus -> infoDto.setOrderShowStatus(ShowStatusEnum.valueOf(orderShowStatus).getTrainMessage()));
    };

    private static final BiConsumer<OrderInfoDto, OrderDto> HOTEL = (infoDto, order) -> {
        Optional.ofNullable(order.getOrderDetail())
                .flatMap(orderDetailDto -> Optional.ofNullable(orderDetailDto.getHotelOrderDetailDto()))
                .ifPresent(hotelOrderDetailDto -> {
                    infoDto.setHotelName(hotelOrderDetailDto.getHotelName());
                    infoDto.setHotelAddress(hotelOrderDetailDto.getHotelAddress());
                    int daysBetween = DateUtils.getDaysBetween(hotelOrderDetailDto.getCheckInDate(), hotelOrderDetailDto.getCheckOutDate());
                    infoDto.setStayDays(daysBetween);
                });
        Optional.ofNullable(order.getOrderShowStatus())
                .ifPresent(orderShowStatus -> infoDto.setOrderShowStatus(ShowStatusEnum.valueOf(orderShowStatus).getHotelMessage()));
    };

    private static final BiConsumer<OrderInfoDto, OrderDto> INSURANCE = (infoDto, order) -> {
        Optional.ofNullable(order.getInsuranceProductInfoDtos())
                .flatMap(insuranceProductInfoDtos -> insuranceProductInfoDtos.stream().findFirst())
                .ifPresent(insuranceProductInfoDto -> infoDto.setInsuranceName(insuranceProductInfoDto.getInsuranceName()));
        Optional.ofNullable(order.getOrderShowStatus())
                .ifPresent(orderShowStatus -> infoDto.setOrderShowStatus(ShowStatusEnum.valueOf(orderShowStatus).getInsuranceMessage()));
    };

    private static final BiConsumer<OrderInfoDto, OrderDto> GENERAL = (infoDto, order) -> {
        Optional.ofNullable(order.getGeneralProductInfoDto()).ifPresent(generalProductInfoDto -> infoDto.setServiceName(generalProductInfoDto.getServiceName()));
        Optional.ofNullable(order.getOrderShowStatus())
                .ifPresent(orderShowStatus -> infoDto.setOrderShowStatus(ShowStatusEnum.valueOf(orderShowStatus).getGeneralMessage()));
    };

    private final static Map<OrderBizType, BiConsumer<OrderInfoDto, OrderDto>> OF = Maps.newHashMap();

    static {
        OF.put(OrderBizType.FLIGHT, FLIGHT);
        OF.put(OrderBizType.HOTEL, HOTEL);
        OF.put(OrderBizType.TRAIN, TRAIN);
        OF.put(OrderBizType.INSURANCE, INSURANCE);
        OF.put(OrderBizType.INTL_FLIGHT, INTL_FLIGHT);
        OF.put(OrderBizType.GENERAL, GENERAL);
    }

    public ResponseDto getOrderInfo(Long userId) {
        ResponseDto<UserDto> userDtoResponse = userService.getUserBaseInfo(userId);
        if (userDtoResponse.isNotSuccess() || Objects.isNull(userDtoResponse.getData())) {
            return userDtoResponse;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 3);
        ResponseDto<List<OrderDto>> listResponseDto = orderService
                .queryOrderListByCs(userDtoResponse.getData().getPartnerId(), userId, calendar.getTime());
        if (listResponseDto.isNotSuccess() || CollectionUtils.isEmpty(listResponseDto.getData())) {
            return listResponseDto;
        }
        List<OrderInfoDto> collect = listResponseDto.getData().stream()
                .filter(orderDto -> !StringUtils.equals(orderDto.getOrderShowStatus(), CANCELED))
                .filter(orderDto -> Objects.nonNull(orderDto.getTravelStartTime()))
//                .filter(orderDto -> Objects.nonNull(orderDto.getProductInfos()))
                .map(orderDto -> {
                    OrderInfoDto orderInfoDto = new OrderInfoDto();
                    orderInfoDto.setId(orderDto.getId());

                    Integer bizType = orderDto.getBizType();
                    orderInfoDto.setBizType(OrderBizType.getBizTypeCode(bizType));

                    orderInfoDto.setTotalAmount(orderDto.getTotalAmount());

                    OrderBizType orderBizType = getOrderBizTypeByCode(bizType);
                    if (!OF.containsKey(orderBizType)) {
                        return null;
                    }
                    OF.get(orderBizType).accept(orderInfoDto, orderDto);

                    orderInfoDto.setTravelStartTime(orderDto.getTravelStartTime());

                    Optional.ofNullable(orderDto.getOrderTravellerNames())
                            .ifPresent(orderInfoDto::setUserName);

                    return orderInfoDto;
                })
                .filter(Objects::nonNull)
                .sorted((d1, d2) -> d2.getTravelStartTime().compareTo(d1.getTravelStartTime()))
                .limit(20)
                .collect(Collectors.toList());
        return ResponseDto.success(collect);
    }

    private static OrderBizType getOrderBizTypeByCode(Integer bizType) {
        return Arrays.stream(OrderBizType.values()).filter(orderBizType -> orderBizType.getCode() == bizType).findFirst().orElse(null);
    }

    /**
     * 获取用户和差标基本信息
     *
     */
    public UserAndStandardForm getUserAndStandardInfo(Long userId) {
        UserDto userDto = userService.getUserBaseInfo(userId).getData();
        PartnerDto partnerDto = partnerService.findById(userDto.getPartnerId()).getData();
        List<UserPartnerDto> selectablePartners = partnerService.findSelectablePartner(userId).getData();

        UserAndStandardForm form = new UserAndStandardForm();
        form.setUserId(userId);
        form.setUserName(userDto.getFullname());
        form.setMobile(userDto.getMobile());
        form.setEmail(userDto.getEmail());
        form.setAllPartnerList(selectablePartners);
        form.setPartnerRemarks(partnerDto.getRemarks());
        if (Objects.equals(userDto.getGender(), 0)) {
            form.setGender("男");
        } else if (Objects.equals(userDto.getGender(), 1)) {
            form.setGender("女");
        }
        form.setVipLevel(userDto.getVipLevel());
        form.setPartnerId(userDto.getPartnerId());
        form.setPartnerName(partnerDto.getName());
        if (partnerDto.getManagerId() != null) {
            UserDto salerUserDto = userService.getUserBaseInfo(partnerDto.getSalerId()).getData();
            if (salerUserDto != null) {
                form.setSalerName(salerUserDto.getFullname());
            }
        }
        if (partnerDto.getManagerId() != null) {
            UserDto managerUserDto = userService.getUserBaseInfo(partnerDto.getManagerId()).getData();
            if (managerUserDto != null) {
                form.setManagerName(managerUserDto.getFullname());
            }
        }
        if (partnerDto.getPartnerChannel() != null) {
            Map<String, String> channelMap = dictService.getCodesMap("PARTNER_CHANNEL").getData();
            form.setPartnerChannelName(channelMap.get(partnerDto.getPartnerChannel()));
        }
        if (userDto.getOrgId() != null) {
            OrgDto orgDto = orgService.getById(userDto.getOrgId()).getData();
            if (orgDto != null) {
                form.setOrgPathNames(orgService.getOrgNameByPath(orgDto.getPath()).getData());
            }
        }

        //员工备注
        List<UserTagDto> tags = userService.findUserTags(userId).getData();
        if (tags != null) {
            form.setTags(tags.stream().filter(tag -> 1 == tag.getStatus()).collect(Collectors.toList()));
        } else {
            form.setTags(new ArrayList<>());
        }

        // 【差旅管控老接口】String empLevelCode = userDataService.getEmpLevel(userDto.getPartnerId(), userDto.getId()).getData();
        String empLevelCode = partnerRankService.getEmpRank(userDto.getPartnerId(), userDto.getId()).getData();
        LogUtils.info(log, "云客服，获取用户和差标基本信息，userId:{},openId:{}，【差旅管控新接口PartnerRankService#getEmpRank(java.lang.Long, java.lang.Long)】empLevelCode：{}", userId, empLevelCode);
        if (StringUtils.isEmpty(empLevelCode)) {
            LogUtils.info(log, "员工职级信息为空，直接返回数据:{}", form);
            return form;
        }
        String empLevelName = dictService.getPartnerCodeTxt(userDto.getPartnerId(), "TM_EMPLOYEE_LEVEL", empLevelCode).getData();
        form.setEmpLevelName(empLevelName);

        return form;
    }



}

