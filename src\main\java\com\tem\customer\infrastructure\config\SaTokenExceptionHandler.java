package com.tem.customer.infrastructure.config;

import cn.dev33.satoken.exception.DisableServiceException;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.exception.SaTokenException;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Sa-Token全局异常处理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 优先级高于其他异常处理器
public class SaTokenExceptionHandler {

    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public Result<Object> handleNotLoginException(NotLoginException e) {
        LogUtils.warn(log, "Sa-Token未登录异常: {}", e.getMessage());

        String message = switch (e.getType()) {
            case NotLoginException.NOT_TOKEN -> "未提供Token";
            case NotLoginException.INVALID_TOKEN -> "Token无效";
            case NotLoginException.TOKEN_TIMEOUT -> "Token已过期";
            case NotLoginException.BE_REPLACED -> "Token已被顶下线";
            case NotLoginException.KICK_OUT -> "Token已被踢下线";
            default -> "当前会话未登录";
        };

        return Result.error(ResultCode.UNAUTHORIZED.getCode(), message);
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public Result<Object> handleNotPermissionException(NotPermissionException e) {
        LogUtils.warn(log, "Sa-Token权限不足异常: {}", e.getMessage());
        return Result.error(ResultCode.FORBIDDEN.getCode(), "权限不足: " + e.getPermission());
    }

    /**
     * 处理角色不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    public Result<Object> handleNotRoleException(NotRoleException e) {
        LogUtils.warn(log, "Sa-Token角色不足异常: {}", e.getMessage());
        return Result.error(ResultCode.FORBIDDEN.getCode(), "角色不足: " + e.getRole());
    }

    /**
     * 处理服务封禁异常
     */
    @ExceptionHandler(DisableServiceException.class)
    public Result<Object> handleDisableServiceException(DisableServiceException e) {
        LogUtils.warn(log, "Sa-Token服务封禁异常: {}", e.getMessage());
        return Result.error(ResultCode.FORBIDDEN.getCode(), "当前账号服务已被封禁: " + e.getService());
    }

    /**
     * 处理Sa-Token其他异常
     */
    @ExceptionHandler(SaTokenException.class)
    public Result<Object> handleSaTokenException(SaTokenException e) {
        LogUtils.error(log, "Sa-Token异常: {}", e.getMessage(), e);
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "认证服务异常: " + e.getMessage());
    }
}
