package com.tem.customer.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 线程池配置属性类
 * 支持多个线程池的配置管理
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@Component
@ConfigurationProperties(prefix = "benefits.thread-pool")
public class ThreadPoolProperties {

    /**
     * 是否启用线程池监控
     */
    private boolean monitorEnabled = true;

    /**
     * 监控统计间隔（秒）
     */
    private long monitorInterval = 30;

    /**
     * 是否启用JMX监控
     */
    private boolean jmxEnabled = true;

    /**
     * 核心线程数
     */
    private int coreSize = 10;

    /**
     * 最大线程数
     */
    private int maxSize = 50;

    /**
     * 队列容量
     */
    private int queueCapacity = 1000;

    /**
     * 线程空闲时间
     */
    private long keepAliveTime = 60;

    /**
     * 时间单位
     */
    private TimeUnit timeUnit = TimeUnit.SECONDS;

    /**
     * 线程名前缀
     */
    private String threadNamePrefix = "benefits-pool";

    /**
     * 是否为守护线程
     */
    private boolean daemon = false;

    /**
     * 线程优先级
     */
    private int threadPriority = Thread.NORM_PRIORITY;

    /**
     * 拒绝策略类型
     */
    private RejectedExecutionHandlerType rejectedExecutionHandlerType = RejectedExecutionHandlerType.ABORT_POLICY;

    /**
     * 是否允许核心线程超时
     */
    private boolean allowCoreThreadTimeOut = false;

    /**
     * 是否预启动所有核心线程
     */
    private boolean prestartAllCoreThreads = false;

    /**
     * 是否启用TTL支持
     */
    private boolean ttlEnabled = true;

    /**
     * 拒绝策略枚举
     */
    public enum RejectedExecutionHandlerType {
        /**
         * 抛出异常
         */
        ABORT_POLICY,

        /**
         * 调用者运行
         */
        CALLER_RUNS_POLICY,

        /**
         * 丢弃任务
         */
        DISCARD_POLICY,

        /**
         * 丢弃最老的任务
         */
        DISCARD_OLDEST_POLICY
    }

    /**
     * 获取默认线程池配置
     */
    public PoolConfig getDefaultPoolConfig() {
        PoolConfig config = new PoolConfig();
        config.setCoreSize(this.coreSize);
        config.setMaxSize(this.maxSize);
        config.setQueueCapacity(this.queueCapacity);
        config.setKeepAliveTime(this.keepAliveTime);
        config.setTimeUnit(this.timeUnit);
        config.setThreadNamePrefix(this.threadNamePrefix);
        config.setDaemon(this.daemon);
        config.setThreadPriority(this.threadPriority);
        config.setRejectedExecutionHandlerType(this.rejectedExecutionHandlerType);
        config.setAllowCoreThreadTimeOut(this.allowCoreThreadTimeOut);
        config.setPrestartAllCoreThreads(this.prestartAllCoreThreads);
        config.setTtlEnabled(this.ttlEnabled);
        return config;
    }

    /**
     * 线程池配置内部类
     */
    @Data
    public static class PoolConfig {
        private int coreSize;
        private int maxSize;
        private int queueCapacity;
        private long keepAliveTime;
        private TimeUnit timeUnit;
        private String threadNamePrefix;
        private boolean daemon;
        private int threadPriority;
        private RejectedExecutionHandlerType rejectedExecutionHandlerType;
        private boolean allowCoreThreadTimeOut;
        private boolean prestartAllCoreThreads;
        private boolean ttlEnabled;
    }
}
