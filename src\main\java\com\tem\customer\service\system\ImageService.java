package com.tem.customer.service.system;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * {@code @Author:} fumouren
 * {@code @CreateTime:} 2025-06-20 11:32
 * {@code @Description:}
 */
public interface ImageService {

    String uploadImage(MultipartFile file) throws IOException;

    /**
     * 批量上传图片
     *
     * @param files 图片文件列表
     * @return 批量上传结果，包含成功和失败的文件信息
     */
    Map<String, Object> uploadImages(List<MultipartFile> files);
}
