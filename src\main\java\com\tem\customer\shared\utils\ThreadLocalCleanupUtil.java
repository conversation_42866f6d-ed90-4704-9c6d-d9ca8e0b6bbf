package com.tem.customer.shared.utils;

import com.iplatform.common.utils.LogUtils;
import com.tem.platform.security.authorize.ContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * ThreadLocal 清理工具类
 * 用于强制清理各种 ThreadLocal 数据，防止线程复用时的数据污染
 * 
 * <p>主要功能：</p>
 * <ul>
 *   <li>清理 ContextHolder 中的 ThreadLocal 数据</li>
 *   <li>清理 UserContextUtil 中的 ThreadLocal 数据</li>
 *   <li>提供反射方式的强制清理（兜底方案）</li>
 *   <li>记录详细的清理日志用于问题排查</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
public class ThreadLocalCleanupUtil {

    /**
     * 执行完整的 ThreadLocal 清理
     * 这是主要的清理入口方法
     */
    public static void performCompleteCleanup() {
        long threadId = Thread.currentThread().getId();
        String threadName = Thread.currentThread().getName();
        
        LogUtils.debug(log, "开始执行完整的ThreadLocal清理 - 线程ID: {}, 线程名: {}", threadId, threadName);
        
        try {
            // 1. 清理 ContextHolder
            cleanupContextHolder();
            
            // 2. 清理 UserContextUtil 中的 ThreadLocal
            cleanupUserContextUtil();
            
            // 3. 强制清理其他可能的 ThreadLocal（反射方式）
            performReflectiveCleanup();
            
            LogUtils.debug(log, "ThreadLocal清理完成 - 线程ID: {}, 线程名: {}", threadId, threadName);
            
        } catch (Exception e) {
            LogUtils.error(log, "ThreadLocal清理过程中发生异常 - 线程ID: {}, 线程名: {}", 
                    threadId, threadName, e);
        }
    }

    /**
     * 清理 ContextHolder 中的 ThreadLocal 数据
     */
    private static void cleanupContextHolder() {
        try {
            // 记录清理前的状态
            try {
                Object currentContext = ContextHolder.getContext();
                if (currentContext != null) {
                    LogUtils.debug(log, "清理前ContextHolder中有上下文数据");
                } else {
                    LogUtils.debug(log, "清理前ContextHolder中无上下文数据");
                }
            } catch (Exception e) {
                LogUtils.debug(log, "获取ContextHolder当前状态时发生异常", e);
            }
            
            // 尝试调用 clearContext 方法
            try {
                ContextHolder.clearContext();
                LogUtils.debug(log, "ContextHolder.clearContext() 调用成功");
            } catch (Exception e) {
                LogUtils.warn(log, "ContextHolder.clearContext() 调用失败，尝试反射清理", e);
                cleanupContextHolderByReflection();
            }
            
        } catch (Exception e) {
            LogUtils.error(log, "清理ContextHolder时发生异常", e);
        }
    }

    /**
     * 通过反射方式清理 ContextHolder
     */
    private static void cleanupContextHolderByReflection() {
        try {
            Class<?> contextHolderClass = ContextHolder.class;
            
            // 查找所有可能的 ThreadLocal 字段
            Field[] fields = contextHolderClass.getDeclaredFields();
            for (Field field : fields) {
                if (ThreadLocal.class.isAssignableFrom(field.getType())) {
                    field.setAccessible(true);
                    ThreadLocal<?> threadLocal = (ThreadLocal<?>) field.get(null);
                    if (threadLocal != null) {
                        threadLocal.remove();
                        LogUtils.debug(log, "通过反射清理ContextHolder中的ThreadLocal字段: {}", field.getName());
                    }
                }
            }
            
        } catch (Exception e) {
            LogUtils.warn(log, "通过反射清理ContextHolder失败", e);
        }
    }

    /**
     * 清理 UserContextUtil 中的 ThreadLocal 数据
     */
    private static void cleanupUserContextUtil() {
        try {
            // 清理 responseLocal
            UserContextUtil.clearResponse();
            LogUtils.debug(log, "UserContextUtil.clearResponse() 调用成功");
            
        } catch (Exception e) {
            LogUtils.warn(log, "清理UserContextUtil时发生异常", e);
        }
    }

    /**
     * 执行反射方式的强制清理
     * 这是一个兜底方案，用于清理可能遗漏的 ThreadLocal
     */
    private static void performReflectiveCleanup() {
        try {
            // 获取当前线程的 ThreadLocalMap
            Thread currentThread = Thread.currentThread();
            Field threadLocalsField = Thread.class.getDeclaredField("threadLocals");
            threadLocalsField.setAccessible(true);
            Object threadLocalMap = threadLocalsField.get(currentThread);
            
            if (threadLocalMap != null) {
                // 获取 ThreadLocalMap 的 table 字段
                Class<?> threadLocalMapClass = threadLocalMap.getClass();
                Field tableField = threadLocalMapClass.getDeclaredField("table");
                tableField.setAccessible(true);
                Object[] table = (Object[]) tableField.get(threadLocalMap);
                
                if (table != null) {
                    int cleanedCount = 0;
                    for (Object entry : table) {
                        if (entry != null) {
                            // 检查是否是我们关心的 ThreadLocal
                            Field referentField = entry.getClass().getSuperclass().getDeclaredField("referent");
                            referentField.setAccessible(true);
                            Object threadLocal = referentField.get(entry);
                            
                            if (threadLocal != null) {
                                String threadLocalClassName = threadLocal.getClass().getName();
                                // 只清理我们关心的 ThreadLocal
                                if (threadLocalClassName.contains("ContextHolder") || 
                                    threadLocalClassName.contains("UserContext")) {
                                    
                                    // 清理这个 ThreadLocal
                                    Method removeMethod = ThreadLocal.class.getDeclaredMethod("remove");
                                    removeMethod.invoke(threadLocal);
                                    cleanedCount++;
                                    
                                    LogUtils.debug(log, "通过反射清理ThreadLocal: {}", threadLocalClassName);
                                }
                            }
                        }
                    }
                    
                    if (cleanedCount > 0) {
                        LogUtils.debug(log, "反射方式清理了 {} 个ThreadLocal", cleanedCount);
                    }
                }
            }
            
        } catch (Exception e) {
            LogUtils.debug(log, "反射方式清理ThreadLocal时发生异常（这通常是正常的）", e);
        }
    }

    /**
     * 检查当前线程是否有残留的用户上下文
     * 用于调试和监控
     * 
     * @return 如果有残留数据返回 true
     */
    public static boolean hasRemainingUserContext() {
        try {
            // 检查 ContextHolder
            Object context = ContextHolder.getContext();
            if (context != null) {
                LogUtils.warn(log, "发现残留的ContextHolder用户上下文");
                return true;
            }

            return false;

        } catch (Exception e) {
            LogUtils.debug(log, "检查残留用户上下文时发生异常", e);
            return false;
        }
    }
}
