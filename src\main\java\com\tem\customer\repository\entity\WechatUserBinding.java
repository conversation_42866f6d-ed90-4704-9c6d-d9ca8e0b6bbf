package com.tem.customer.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 微信用户绑定关系实体类
 * 支持企业微信、公众号、小程序等多种来源的用户绑定关系管理
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_wechat_user_binding")
public class WechatUserBinding extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业ID，关联企业表
     */
    @TableField("partner_id")
    private Long partnerId;

    /**
     * 微信UnionID
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 来源类型：WORK_WECHAT-企业微信，OFFICIAL_ACCOUNT-公众号，MINI_PROGRAM-小程序，WECHAT_H5-微信H5，GROUP_STAFF-群内人员
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 来源应用ID（公众号appid、小程序appid等）
     */
    @TableField("source_app_id")
    private String sourceAppId;

    /**
     * 绑定的用户ID（SaaS系统中的用户）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 企业微信群ID，用于群内人员绑定场景
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 状态：1-有效，0-无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 来源类型枚举
     */
    @Getter
    public enum SourceType {
        /**
         * 来源类型
         */
        WORK_WECHAT("WORK_WECHAT", "企业微信"),
        OFFICIAL_ACCOUNT("OFFICIAL_ACCOUNT", "公众号"),
        MINI_PROGRAM("MINI_PROGRAM", "小程序"),
        WECHAT_H5("WECHAT_H5", "微信H5"),
        GROUP_STAFF("GROUP_STAFF", "群内人员");

        private final String code;
        private final String desc;

        SourceType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    /**
     * 状态枚举
     */
    @Getter
    public enum Status {
        /**
         * 状态
         */
        INVALID(0, "无效"),
        VALID(1, "有效");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }
}
