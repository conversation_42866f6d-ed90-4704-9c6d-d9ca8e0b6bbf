package com.tem.customer.service.partner;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.model.vo.partner.PartnerWechatGroupVO;

import java.util.List;

/**
 * 企业微信群绑定关系服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface PartnerWechatGroupService extends IService<PartnerWechatGroup> {

    /**
     * 根据企业ID查询微信群列表
     *
     * @param partnerId 企业ID
     * @return 微信群列表，按排序字段升序排列
     */
    List<PartnerWechatGroup> listByPartnerId(Long partnerId);

    /**
     * 根据企业ID查询启用状态的微信群列表
     *
     * @param partnerId 企业ID
     * @return 启用状态的微信群列表，按排序字段升序排列
     */
    List<PartnerWechatGroup> listEnabledByPartnerId(Long partnerId);

    /**
     * 根据企业ID统计微信群数量
     *
     * @param partnerId 企业ID
     * @return 微信群数量
     */
    int countByPartnerId(Long partnerId);

    /**
     * 根据企业ID统计启用状态的微信群数量
     *
     * @param partnerId 企业ID
     * @return 启用状态的微信群数量
     */
    int countEnabledByPartnerId(Long partnerId);

    /**
     * 根据chatId查询微信群信息
     *
     * @param chatId 企业微信群ID
     * @return 微信群信息
     */
    PartnerWechatGroup getByChatId(String chatId);

    /**
     * 根据chatId查询微信群VO信息，包含企业名称
     *
     * @param chatId 企业微信群ID
     * @return 微信群VO信息，包含企业名称
     */
    PartnerWechatGroupVO getByChatIdVO(String chatId);

    /**
     * 创建企业微信群绑定关系
     *
     * @param partnerWechatGroup 企业微信群绑定关系
     * @return 创建结果
     */
    PartnerWechatGroup createPartnerWechatGroup(PartnerWechatGroup partnerWechatGroup);

    /**
     * 更新企业微信群绑定关系
     *
     * @param partnerWechatGroup 企业微信群绑定关系
     * @return 更新结果
     */
    boolean updatePartnerWechatGroup(PartnerWechatGroup partnerWechatGroup);

    /**
     * 删除企业微信群绑定关系
     *
     * @param id 记录ID
     * @return 删除结果
     */
    boolean deletePartnerWechatGroup(Long id);

    /**
     * 启用/禁用企业微信群
     *
     * @param id 记录ID
     * @param status 状态：1-启用，0-禁用
     * @return 操作结果
     */
    boolean updateStatus(Long id, Integer status);

    /**
     * 调整企业微信群排序
     *
     * @param id 记录ID
     * @param sortOrder 新的排序值
     * @return 操作结果
     */
    boolean updateSortOrder(Long id, Integer sortOrder);

    /**
     * 检查chatId是否已存在
     *
     * @param chatId 企业微信群ID
     * @param excludeId 排除的记录ID（更新时使用）
     * @return 是否存在
     */
    boolean existsByChatId(String chatId, Long excludeId);
}
