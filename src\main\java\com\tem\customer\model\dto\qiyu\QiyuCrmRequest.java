package com.tem.customer.model.dto.qiyu;

import lombok.Data;

/**
 * 七鱼CRM请求基础对象
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class QiyuCrmRequest {

    /**
     * 企业分配给网易七鱼系统的 appid
     */
    private String appid;

    /**
     * 当前有效的 token
     */
    private String token;

    /**
     * 用户的唯一性标识
     */
    private String userid;

    /**
     * 当前 UTC 时间戳（新版认证方式）
     */
    private Long time;

    /**
     * 签名校验值（新版认证方式）
     */
    private String checksum;

    /**
     * 企业分配给网易七鱼系统的 appsecret（获取token时使用）
     */
    private String appsecret;

    /**
     * 验证请求的基本参数
     * 
     * @return 验证结果
     */
    public boolean validateBasicParams() {
        return appid != null && !appid.trim().isEmpty() 
            && token != null && !token.trim().isEmpty();
    }

    /**
     * 验证新版认证参数
     * 
     * @return 验证结果
     */
    public boolean validateNewAuthParams() {
        return time != null && checksum != null && !checksum.trim().isEmpty();
    }

    /**
     * 验证获取token参数
     * 
     * @return 验证结果
     */
    public boolean validateTokenParams() {
        return appid != null && !appid.trim().isEmpty() 
            && appsecret != null && !appsecret.trim().isEmpty();
    }
}
