<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.tem.customer</groupId>
    <artifactId>customer-admin-web</artifactId>
    <version>${revision}</version>
    <name>customer-admin-web</name>
    <description>customer-admin-web</description>

    <properties>
        <revision>0.1.1-SNAPSHOT</revision>
        <java.version>21</java.version>
        <spring-boot.version>3.5.0</spring-boot.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <dubbo.version>3.3.5</dubbo.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <commons.io.version>2.19.0</commons.io.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <apollo.version>2.4.0</apollo.version>
        <druid.version>1.2.24</druid.version>
        <redisson.version>3.47.0</redisson.version>
        <google.guava.version>33.4.8-jre</google.guava.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <ttl.version>2.14.5</ttl.version>
        <saToken.version>1.44.0</saToken.version>


        <!-- 内部包版本 -->
        <oms-api.version>4.5.5-SNAPSHOT</oms-api.version>
        <imgserver-client.version>1.2.0-SNAPSHOT</imgserver-client.version>
        <platform-common.version>3.2.9-jdk21-SNAPSHOT</platform-common.version>
        <platform-common-web.version>3.2.3-jdk21-SNAPSHOT</platform-common-web.version>
        <platform-common-trace.version>3.3.0-jdk21-SNAPSHOT</platform-common-trace.version>
        <iPlatform-common-router.version>3.2.8-jdk21-SNAPSHOT</iPlatform-common-router.version>
        <platform-authorize.version>3.0.0-jdk21-SNAPSHOT</platform-authorize.version>
        <platform-authentication.version>3.0.0-jdk21-SNAPSHOT</platform-authentication.version>
        <platform-api.version>3.34.95-SNAPSHOT</platform-api.version>
        <member-api.version>1.3.3-SNAPSHOT</member-api.version>
        <quark-api.version>1.2.6-SNAPSHOT</quark-api.version>
        <butler-api.version>1.1.0-SNAPSHOT</butler-api.version>
        <ki4so.version>2.2.0-jdk21-SNAPSHOT</ki4so.version>
        <otapub-share-api.version>1.1.35-SNAPSHOT</otapub-share-api.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Dubbo -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>
                <version>3.3.2</version>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
            <version>${saToken.version}</version>
        </dependency>

        <!-- Sa-Token 整合 Redisson （使用 jackson 序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redisson-spring-boot-starter</artifactId>
            <version>${saToken.version}</version>
        </dependency>

        <!-- Spring Session -->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!-- dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>otapub-share-api</artifactId>
            <version>${otapub-share-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- 数据库 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>

        <!-- MyBatis-Plus JSQLParser 依赖，用于分页插件 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- MyBatis-Plus 代码生成器 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- Freemarker 模板引擎，用于代码生成器 -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.33</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ki4so 登录依赖 -->
        <dependency>
            <groupId>com.github.ki4so</groupId>
            <artifactId>ki4so-java-client</artifactId>
            <version>${ki4so.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>platform-authorize</artifactId>
            <version>${platform-authorize.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-transform</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.tem</groupId>
                    <artifactId>platform-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>platform-authentication</artifactId>
            <version>${platform-authentication.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-transform</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.tem</groupId>
                    <artifactId>platform-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.tem</groupId>
                    <artifactId>platform-authorize</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- EasyCaptcha 验证码库 -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- 内部包 -->
        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common</artifactId>
            <version>${platform-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iplatform-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun.openservices</groupId>
                    <artifactId>aliyun-log-log4j-appender</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-router</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-trace</artifactId>
            <version>${platform-common-trace.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-router</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-web</artifactId>
            <version>${platform-common-web.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>platform-api</artifactId>
            <version>3.34.95-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-router</artifactId>
            <version>${iPlatform-common-router.version}</version>
        </dependency>


        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-transform</artifactId>
            <version>3.2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.aliyun.openservices</groupId>
                    <artifactId>aliyun-log-producer</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tem.imgserver</groupId>
            <artifactId>imgserver-client</artifactId>
            <version>${imgserver-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.ki4so</groupId>
                    <artifactId>ki4so-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-fileupload</groupId>
                    <artifactId>commons-fileupload</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>platform-biz-api</artifactId>
            <version>1.5.18-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>oms-api</artifactId>
            <version>${oms-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>iPlatform-common</artifactId>
                    <groupId>com.iplatform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.tem.common</groupId>
                    <artifactId>framework-i18n-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-trace</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.tem</groupId>
                    <artifactId>platform-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-router</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tem.errand</groupId>
            <artifactId>quark-api</artifactId>
            <version>${quark-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tem.errand</groupId>
            <artifactId>butler-api</artifactId>
            <version>${butler-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>member-api</artifactId>
            <version>${member-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>iPlatform-common</artifactId>
                    <groupId>com.iplatform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common-cache-x</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.tem</groupId>
                    <artifactId>platform-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tem</groupId>
            <artifactId>trsso-api</artifactId>
            <version>1.1.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iplatform</groupId>
                    <artifactId>iPlatform-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-idgen</artifactId>
            <version>3.4.2</version>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-idgen-mybatis</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.iplatform</groupId>
            <artifactId>iPlatform-common-global</artifactId>
            <version>1.0.0</version>
        </dependency>


        <!-- 日志 -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-logback-appender</artifactId>
            <version>0.1.29</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- 工具包 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons.io.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${ttl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${google.guava.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>


        <!-- Apache HttpClient 5.x for RestClient -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>

        <!-- 测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter-test</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- spock test -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Groovy 编译器 -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito 用于静态方法mock -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>uat</id>
            <properties>
                <profile.url>http://www.zt.com:18102/nexus/content/repositories/snapshots/</profile.url>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <distributionManagement>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>nexus distribution snapshot repository</name>
            <url>${profile.url}</url>
        </snapshotRepository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus-zt</id>
            <name>Nexus ZT Repository</name>
            <url>http://www.zt.com:18102/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>${java.version}</release>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <showWarnings>true</showWarnings>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- Groovy 编译插件 -->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Surefire 插件配置支持 Groovy 测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*Spec.java</include>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
            </plugin>

            <!-- Exec 插件用于运行代码生成器 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>com.tem.customer.shared.generator.CodeGenerator</mainClass>
                    <classpathScope>compile</classpathScope>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
