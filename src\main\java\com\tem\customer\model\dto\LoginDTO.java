package com.tem.customer.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 登录请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class LoginDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 手机验证码
     */
    @NotBlank(message = "手机验证码不能为空")
    private String smsCode;

    /**
     * 记住我
     */
    private Boolean rememberMe = false;
}
