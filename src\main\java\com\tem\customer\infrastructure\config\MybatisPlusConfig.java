package com.tem.customer.infrastructure.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.iplatform.common.idgen.IdGenerator;
import com.tem.customer.shared.utils.UserContextUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.time.LocalDateTime;

/**
 * MyBatis-Plus统一配置类
 * 包含插件配置、ID生成器、全局配置、SqlSessionFactory和元数据处理器
 *
 * 合并了原 MybatisPlusConfig 和 MybatisPlusGlobalConfig 的所有功能
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Slf4j
@Configuration
public class MybatisPlusConfig {

    @Value("${mybatis-plus.pagination.max-limit:1000}")
    private Long paginationMaxLimit;

    @Value("${mybatis-plus.pagination.optimize-count-sql:true}")
    private Boolean optimizeCountSql;

    @Value("${mybatis-plus.pagination.overflow:false}")
    private Boolean overflow;

    @Value("${mybatis-plus.sql-monitor.enabled:true}")
    private Boolean sqlMonitorEnabled;

    /**
     * MyBatis-Plus插件配置
     * 生产环境配置，包含分页、乐观锁、防全表操作、SQL安全检查等插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 1. 乐观锁插件 - 支持乐观锁机制
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // 2. 防全表操作插件 - 防止误操作全表更新/删除
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

        // 3. 分页插件 - 必须放在最后，避免COUNT SQL执行不准确
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 配置分页参数
        paginationInterceptor.setMaxLimit(paginationMaxLimit); // 单页最大分页条数限制
        paginationInterceptor.setOverflow(overflow); // 溢出总页数后是否进行处理
        paginationInterceptor.setOptimizeJoin(optimizeCountSql); // 自动优化COUNT SQL的JOIN查询
        interceptor.addInnerInterceptor(paginationInterceptor);

        log.info("MyBatis-Plus插件配置完成 - 乐观锁插件、SQL安全检查插件、防全表操作插件、分页插件(maxLimit:{}, overflow:{}, optimizeCountSql:{}, sqlMonitor:{})",
                paginationMaxLimit, overflow, optimizeCountSql, sqlMonitorEnabled);
        return interceptor;
    }


    /**
     * 自定义 ID 生成器 Bean
     * 使用雪花算法生成 ID
     */
    @Bean
    public IdentifierGenerator identifierGenerator(@Qualifier("shortIdWorker") IdGenerator<Long> shortIdWorker) {
        return entity -> shortIdWorker.nextId();
    }

    /**
     * 配置 MyBatis Plus 的全局配置
     * 显式设置 IdentifierGenerator 和 MetaObjectHandler
     */
    @Bean
    public GlobalConfig mpGlobalConfig(IdentifierGenerator identifierGenerator, MetaObjectHandler metaObjectHandler) {
        GlobalConfig globalConfig = new GlobalConfig();

        // 设置自定义的 ID 生成器
        globalConfig.setIdentifierGenerator(identifierGenerator);

        // 设置元数据处理器
        globalConfig.setMetaObjectHandler(metaObjectHandler);

        // 设置 banner
        globalConfig.setBanner(false);

        return globalConfig;
    }

    /**
     * 配置 SqlSessionFactory
     * 确保全局配置被应用
     */
    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource, GlobalConfig globalConfig,
                                               MybatisPlusInterceptor mybatisPlusInterceptor) throws Exception {
        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);

        // 设置全局配置
        sessionFactory.setGlobalConfig(globalConfig);

        // 设置 MyBatis 配置
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
        sessionFactory.setConfiguration(configuration);

        // 添加插件
        sessionFactory.setPlugins(mybatisPlusInterceptor);

        // 设置 mapper 位置
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:com/tem/customer/mapper/*.xml"));

        return sessionFactory.getObject();
    }

    /**
     * 配置参数验证
     */
    @PostConstruct
    public void validateConfig() {
        if (paginationMaxLimit > 5000) {
            log.warn("分页最大限制设置过大，可能影响系统性能: {}", paginationMaxLimit);
        }
        if (paginationMaxLimit < 10) {
            log.warn("分页最大限制设置过小，可能影响用户体验: {}", paginationMaxLimit);
        }
        log.info("MyBatis-Plus配置参数验证完成");
    }


    /**
     * 元数据处理器
     * 自动填充创建时间、更新时间、创建人、更新人等字段
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new ProductionMetaObjectHandler();
    }

    /**
     * 生产环境元数据处理器实现
     */
    private static class ProductionMetaObjectHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            if (log.isDebugEnabled()) {
                log.debug("开始插入填充，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());
            }

            LocalDateTime now = LocalDateTime.now();
            String currentUserId = getCurrentUserId();

            // 自动填充创建时间
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
            this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);

            // 自动填充更新时间
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
            this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);

            // 自动填充创建人
            if (currentUserId != null) {
                this.strictInsertFill(metaObject, "createBy", String.class, currentUserId);
                this.strictInsertFill(metaObject, "createdBy", String.class, currentUserId);
                this.strictInsertFill(metaObject, "updateBy", String.class, currentUserId);
                this.strictInsertFill(metaObject, "updatedBy", String.class, currentUserId);
            }

            // 自动填充逻辑删除标记
            this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
            this.strictInsertFill(metaObject, "isDeleted", Integer.class, 0);

            // 自动填充版本号（乐观锁）
            this.strictInsertFill(metaObject, "version", Integer.class, 1);
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            if (log.isDebugEnabled()) {
                log.debug("开始更新填充，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());
            }

            LocalDateTime now = LocalDateTime.now();
            String currentUserId = getCurrentUserId();

            // 自动填充更新时间
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, now);
            this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, now);

            // 自动填充更新人
            if (currentUserId != null) {
                this.strictUpdateFill(metaObject, "updateBy", String.class, currentUserId);
                this.strictUpdateFill(metaObject, "updatedBy", String.class, currentUserId);
            }
        }

        /**
         * 获取当前登录用户ID
         * 集成platform-authorize实现，生产环境安全获取
         */
        private String getCurrentUserId() {
            try {
                Long userId = UserContextUtil.getCurrentUserId();
                return userId != null ? userId.toString() : null;
            } catch (Exception e) {
                log.warn("获取当前用户ID失败，使用默认值", e);
                return null;
            }
        }
    }


}
