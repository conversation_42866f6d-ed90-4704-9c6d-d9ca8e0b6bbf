package com.tem.customer.model.dto.partner;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;

/**
 * 企业备注请求DTO
 * 用于接收前端请求参数
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@Accessors(chain = true)
public class PartnerNoteDTO {

    /**
     * 备注ID（更新时必填）
     */
    private Long id;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long partnerId;

    /**
     * 备注标题，最大15个字符
     */
    @NotBlank(message = "备注标题不能为空")
    @Size(max = 15, message = "备注标题长度不能超过15个字符")
    private String title;

    /**
     * 备注内容，支持Markdown格式
     */
    @Size(max = 65535, message = "备注内容长度不能超过65535个字符")
    private String content;

    /**
     * 排序字段，数值越小越靠前
     */
    @Min(value = 0, message = "排序值不能为负数")
    private Integer sortOrder;
}
