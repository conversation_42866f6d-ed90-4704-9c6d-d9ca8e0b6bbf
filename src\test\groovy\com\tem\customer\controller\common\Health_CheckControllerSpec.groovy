package com.tem.customer.controller.common

import spock.lang.Specification

/**
 * 健康检查控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class Health_CheckControllerSpec extends Specification {

    def "测试健康检查接口"() {
        given: "创建控制器实例"
        def healthCheckController = new Health_CheckController()

        when: "调用健康检查接口"
        def result = healthCheckController.ping()

        then: "验证返回结果"
        result == "PONG"
    }
}